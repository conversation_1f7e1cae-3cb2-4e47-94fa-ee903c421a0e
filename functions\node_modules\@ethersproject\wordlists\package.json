{"_browser-all": {"./lib/wordlists": "./lib/wordlists.js"}, "_ethers.alias": {"wordlists.js": "browser-wordlists.js"}, "author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}, "description": "Word lists for BIP39 wallets.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "ec1b9583039a14a0e0fa15d0a2a6082a2f41cf5b", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/wordlists", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/wordlists", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x7f753c8f34ec57f7fe3d96c39b7787d3d543b398a152fcb5b2548ef20098d3e1", "types": "./lib/index.d.ts", "version": "5.7.0"}