{"version": 3, "file": "elliptic.js", "sources": ["../../../node_modules/minimalistic-assert/index.js", "../../../node_modules/minimalistic-crypto-utils/lib/utils.js", "../../../node_modules/elliptic/lib/elliptic/utils.js", "../../../node_modules/elliptic/lib/elliptic/curve/base.js", "../../../node_modules/inherits/inherits_browser.js", "../../../node_modules/elliptic/lib/elliptic/curve/short.js", "../../../node_modules/elliptic/lib/elliptic/curve/index.js", "../../../node_modules/elliptic/lib/elliptic/curves.js", "../../../node_modules/hmac-drbg/lib/hmac-drbg.js", "../../../node_modules/elliptic/lib/elliptic/ec/key.js", "../../../node_modules/elliptic/lib/elliptic/ec/signature.js", "../../../node_modules/elliptic/lib/elliptic/ec/index.js", "../../../node_modules/elliptic/lib/elliptic.js", "elliptic.js"], "sourcesContent": ["module.exports = assert;\n\nfunction assert(val, msg) {\n  if (!val)\n    throw new Error(msg || 'Asser<PERSON> failed');\n}\n\nassert.equal = function assertEqual(l, r, msg) {\n  if (l != r)\n    throw new Error(msg || ('<PERSON><PERSON><PERSON> failed: ' + l + ' != ' + r));\n};\n", "'use strict';\n\nvar utils = exports;\n\nfunction toArray(msg, enc) {\n  if (Array.isArray(msg))\n    return msg.slice();\n  if (!msg)\n    return [];\n  var res = [];\n  if (typeof msg !== 'string') {\n    for (var i = 0; i < msg.length; i++)\n      res[i] = msg[i] | 0;\n    return res;\n  }\n  if (enc === 'hex') {\n    msg = msg.replace(/[^a-z0-9]+/ig, '');\n    if (msg.length % 2 !== 0)\n      msg = '0' + msg;\n    for (var i = 0; i < msg.length; i += 2)\n      res.push(parseInt(msg[i] + msg[i + 1], 16));\n  } else {\n    for (var i = 0; i < msg.length; i++) {\n      var c = msg.charCodeAt(i);\n      var hi = c >> 8;\n      var lo = c & 0xff;\n      if (hi)\n        res.push(hi, lo);\n      else\n        res.push(lo);\n    }\n  }\n  return res;\n}\nutils.toArray = toArray;\n\nfunction zero2(word) {\n  if (word.length === 1)\n    return '0' + word;\n  else\n    return word;\n}\nutils.zero2 = zero2;\n\nfunction toHex(msg) {\n  var res = '';\n  for (var i = 0; i < msg.length; i++)\n    res += zero2(msg[i].toString(16));\n  return res;\n}\nutils.toHex = toHex;\n\nutils.encode = function encode(arr, enc) {\n  if (enc === 'hex')\n    return toHex(arr);\n  else\n    return arr;\n};\n", "'use strict';\n\nvar utils = exports;\nvar BN = require('bn.js');\nvar minAssert = require('minimalistic-assert');\nvar minUtils = require('minimalistic-crypto-utils');\n\nutils.assert = minAssert;\nutils.toArray = minUtils.toArray;\nutils.zero2 = minUtils.zero2;\nutils.toHex = minUtils.toHex;\nutils.encode = minUtils.encode;\n\n// Represent num in a w-NAF form\nfunction getNAF(num, w, bits) {\n  var naf = new Array(Math.max(num.bitLength(), bits) + 1);\n  naf.fill(0);\n\n  var ws = 1 << (w + 1);\n  var k = num.clone();\n\n  for (var i = 0; i < naf.length; i++) {\n    var z;\n    var mod = k.andln(ws - 1);\n    if (k.isOdd()) {\n      if (mod > (ws >> 1) - 1)\n        z = (ws >> 1) - mod;\n      else\n        z = mod;\n      k.isubn(z);\n    } else {\n      z = 0;\n    }\n\n    naf[i] = z;\n    k.iushrn(1);\n  }\n\n  return naf;\n}\nutils.getNAF = getNAF;\n\n// Represent k1, k2 in a Joint Sparse Form\nfunction getJSF(k1, k2) {\n  var jsf = [\n    [],\n    [],\n  ];\n\n  k1 = k1.clone();\n  k2 = k2.clone();\n  var d1 = 0;\n  var d2 = 0;\n  var m8;\n  while (k1.cmpn(-d1) > 0 || k2.cmpn(-d2) > 0) {\n    // First phase\n    var m14 = (k1.andln(3) + d1) & 3;\n    var m24 = (k2.andln(3) + d2) & 3;\n    if (m14 === 3)\n      m14 = -1;\n    if (m24 === 3)\n      m24 = -1;\n    var u1;\n    if ((m14 & 1) === 0) {\n      u1 = 0;\n    } else {\n      m8 = (k1.andln(7) + d1) & 7;\n      if ((m8 === 3 || m8 === 5) && m24 === 2)\n        u1 = -m14;\n      else\n        u1 = m14;\n    }\n    jsf[0].push(u1);\n\n    var u2;\n    if ((m24 & 1) === 0) {\n      u2 = 0;\n    } else {\n      m8 = (k2.andln(7) + d2) & 7;\n      if ((m8 === 3 || m8 === 5) && m14 === 2)\n        u2 = -m24;\n      else\n        u2 = m24;\n    }\n    jsf[1].push(u2);\n\n    // Second phase\n    if (2 * d1 === u1 + 1)\n      d1 = 1 - d1;\n    if (2 * d2 === u2 + 1)\n      d2 = 1 - d2;\n    k1.iushrn(1);\n    k2.iushrn(1);\n  }\n\n  return jsf;\n}\nutils.getJSF = getJSF;\n\nfunction cachedProperty(obj, name, computer) {\n  var key = '_' + name;\n  obj.prototype[name] = function cachedProperty() {\n    return this[key] !== undefined ? this[key] :\n      this[key] = computer.call(this);\n  };\n}\nutils.cachedProperty = cachedProperty;\n\nfunction parseBytes(bytes) {\n  return typeof bytes === 'string' ? utils.toArray(bytes, 'hex') :\n    bytes;\n}\nutils.parseBytes = parseBytes;\n\nfunction intFromLE(bytes) {\n  return new BN(bytes, 'hex', 'le');\n}\nutils.intFromLE = intFromLE;\n\n", "'use strict';\n\nvar BN = require('bn.js');\nvar utils = require('../utils');\nvar getNAF = utils.getNAF;\nvar getJSF = utils.getJSF;\nvar assert = utils.assert;\n\nfunction BaseCurve(type, conf) {\n  this.type = type;\n  this.p = new BN(conf.p, 16);\n\n  // Use Montgomery, when there is no fast reduction for the prime\n  this.red = conf.prime ? BN.red(conf.prime) : BN.mont(this.p);\n\n  // Useful for many curves\n  this.zero = new BN(0).toRed(this.red);\n  this.one = new BN(1).toRed(this.red);\n  this.two = new BN(2).toRed(this.red);\n\n  // Curve configuration, optional\n  this.n = conf.n && new BN(conf.n, 16);\n  this.g = conf.g && this.pointFromJSON(conf.g, conf.gRed);\n\n  // Temporary arrays\n  this._wnafT1 = new Array(4);\n  this._wnafT2 = new Array(4);\n  this._wnafT3 = new Array(4);\n  this._wnafT4 = new Array(4);\n\n  this._bitLength = this.n ? this.n.bitLength() : 0;\n\n  // Generalized <PERSON>'s trick\n  var adjustCount = this.n && this.p.div(this.n);\n  if (!adjustCount || adjustCount.cmpn(100) > 0) {\n    this.redN = null;\n  } else {\n    this._maxwellTrick = true;\n    this.redN = this.n.toRed(this.red);\n  }\n}\nmodule.exports = BaseCurve;\n\nBaseCurve.prototype.point = function point() {\n  throw new Error('Not implemented');\n};\n\nBaseCurve.prototype.validate = function validate() {\n  throw new Error('Not implemented');\n};\n\nBaseCurve.prototype._fixedNafMul = function _fixedNafMul(p, k) {\n  assert(p.precomputed);\n  var doubles = p._getDoubles();\n\n  var naf = getNAF(k, 1, this._bitLength);\n  var I = (1 << (doubles.step + 1)) - (doubles.step % 2 === 0 ? 2 : 1);\n  I /= 3;\n\n  // Translate into more windowed form\n  var repr = [];\n  var j;\n  var nafW;\n  for (j = 0; j < naf.length; j += doubles.step) {\n    nafW = 0;\n    for (var l = j + doubles.step - 1; l >= j; l--)\n      nafW = (nafW << 1) + naf[l];\n    repr.push(nafW);\n  }\n\n  var a = this.jpoint(null, null, null);\n  var b = this.jpoint(null, null, null);\n  for (var i = I; i > 0; i--) {\n    for (j = 0; j < repr.length; j++) {\n      nafW = repr[j];\n      if (nafW === i)\n        b = b.mixedAdd(doubles.points[j]);\n      else if (nafW === -i)\n        b = b.mixedAdd(doubles.points[j].neg());\n    }\n    a = a.add(b);\n  }\n  return a.toP();\n};\n\nBaseCurve.prototype._wnafMul = function _wnafMul(p, k) {\n  var w = 4;\n\n  // Precompute window\n  var nafPoints = p._getNAFPoints(w);\n  w = nafPoints.wnd;\n  var wnd = nafPoints.points;\n\n  // Get NAF form\n  var naf = getNAF(k, w, this._bitLength);\n\n  // Add `this`*(N+1) for every w-NAF index\n  var acc = this.jpoint(null, null, null);\n  for (var i = naf.length - 1; i >= 0; i--) {\n    // Count zeroes\n    for (var l = 0; i >= 0 && naf[i] === 0; i--)\n      l++;\n    if (i >= 0)\n      l++;\n    acc = acc.dblp(l);\n\n    if (i < 0)\n      break;\n    var z = naf[i];\n    assert(z !== 0);\n    if (p.type === 'affine') {\n      // J +- P\n      if (z > 0)\n        acc = acc.mixedAdd(wnd[(z - 1) >> 1]);\n      else\n        acc = acc.mixedAdd(wnd[(-z - 1) >> 1].neg());\n    } else {\n      // J +- J\n      if (z > 0)\n        acc = acc.add(wnd[(z - 1) >> 1]);\n      else\n        acc = acc.add(wnd[(-z - 1) >> 1].neg());\n    }\n  }\n  return p.type === 'affine' ? acc.toP() : acc;\n};\n\nBaseCurve.prototype._wnafMulAdd = function _wnafMulAdd(defW,\n  points,\n  coeffs,\n  len,\n  jacobianResult) {\n  var wndWidth = this._wnafT1;\n  var wnd = this._wnafT2;\n  var naf = this._wnafT3;\n\n  // Fill all arrays\n  var max = 0;\n  var i;\n  var j;\n  var p;\n  for (i = 0; i < len; i++) {\n    p = points[i];\n    var nafPoints = p._getNAFPoints(defW);\n    wndWidth[i] = nafPoints.wnd;\n    wnd[i] = nafPoints.points;\n  }\n\n  // Comb small window NAFs\n  for (i = len - 1; i >= 1; i -= 2) {\n    var a = i - 1;\n    var b = i;\n    if (wndWidth[a] !== 1 || wndWidth[b] !== 1) {\n      naf[a] = getNAF(coeffs[a], wndWidth[a], this._bitLength);\n      naf[b] = getNAF(coeffs[b], wndWidth[b], this._bitLength);\n      max = Math.max(naf[a].length, max);\n      max = Math.max(naf[b].length, max);\n      continue;\n    }\n\n    var comb = [\n      points[a], /* 1 */\n      null, /* 3 */\n      null, /* 5 */\n      points[b], /* 7 */\n    ];\n\n    // Try to avoid Projective points, if possible\n    if (points[a].y.cmp(points[b].y) === 0) {\n      comb[1] = points[a].add(points[b]);\n      comb[2] = points[a].toJ().mixedAdd(points[b].neg());\n    } else if (points[a].y.cmp(points[b].y.redNeg()) === 0) {\n      comb[1] = points[a].toJ().mixedAdd(points[b]);\n      comb[2] = points[a].add(points[b].neg());\n    } else {\n      comb[1] = points[a].toJ().mixedAdd(points[b]);\n      comb[2] = points[a].toJ().mixedAdd(points[b].neg());\n    }\n\n    var index = [\n      -3, /* -1 -1 */\n      -1, /* -1 0 */\n      -5, /* -1 1 */\n      -7, /* 0 -1 */\n      0, /* 0 0 */\n      7, /* 0 1 */\n      5, /* 1 -1 */\n      1, /* 1 0 */\n      3,  /* 1 1 */\n    ];\n\n    var jsf = getJSF(coeffs[a], coeffs[b]);\n    max = Math.max(jsf[0].length, max);\n    naf[a] = new Array(max);\n    naf[b] = new Array(max);\n    for (j = 0; j < max; j++) {\n      var ja = jsf[0][j] | 0;\n      var jb = jsf[1][j] | 0;\n\n      naf[a][j] = index[(ja + 1) * 3 + (jb + 1)];\n      naf[b][j] = 0;\n      wnd[a] = comb;\n    }\n  }\n\n  var acc = this.jpoint(null, null, null);\n  var tmp = this._wnafT4;\n  for (i = max; i >= 0; i--) {\n    var k = 0;\n\n    while (i >= 0) {\n      var zero = true;\n      for (j = 0; j < len; j++) {\n        tmp[j] = naf[j][i] | 0;\n        if (tmp[j] !== 0)\n          zero = false;\n      }\n      if (!zero)\n        break;\n      k++;\n      i--;\n    }\n    if (i >= 0)\n      k++;\n    acc = acc.dblp(k);\n    if (i < 0)\n      break;\n\n    for (j = 0; j < len; j++) {\n      var z = tmp[j];\n      p;\n      if (z === 0)\n        continue;\n      else if (z > 0)\n        p = wnd[j][(z - 1) >> 1];\n      else if (z < 0)\n        p = wnd[j][(-z - 1) >> 1].neg();\n\n      if (p.type === 'affine')\n        acc = acc.mixedAdd(p);\n      else\n        acc = acc.add(p);\n    }\n  }\n  // Zeroify references\n  for (i = 0; i < len; i++)\n    wnd[i] = null;\n\n  if (jacobianResult)\n    return acc;\n  else\n    return acc.toP();\n};\n\nfunction BasePoint(curve, type) {\n  this.curve = curve;\n  this.type = type;\n  this.precomputed = null;\n}\nBaseCurve.BasePoint = BasePoint;\n\nBasePoint.prototype.eq = function eq(/*other*/) {\n  throw new Error('Not implemented');\n};\n\nBasePoint.prototype.validate = function validate() {\n  return this.curve.validate(this);\n};\n\nBaseCurve.prototype.decodePoint = function decodePoint(bytes, enc) {\n  bytes = utils.toArray(bytes, enc);\n\n  var len = this.p.byteLength();\n\n  // uncompressed, hybrid-odd, hybrid-even\n  if ((bytes[0] === 0x04 || bytes[0] === 0x06 || bytes[0] === 0x07) &&\n      bytes.length - 1 === 2 * len) {\n    if (bytes[0] === 0x06)\n      assert(bytes[bytes.length - 1] % 2 === 0);\n    else if (bytes[0] === 0x07)\n      assert(bytes[bytes.length - 1] % 2 === 1);\n\n    var res =  this.point(bytes.slice(1, 1 + len),\n      bytes.slice(1 + len, 1 + 2 * len));\n\n    return res;\n  } else if ((bytes[0] === 0x02 || bytes[0] === 0x03) &&\n              bytes.length - 1 === len) {\n    return this.pointFromX(bytes.slice(1, 1 + len), bytes[0] === 0x03);\n  }\n  throw new Error('Unknown point format');\n};\n\nBasePoint.prototype.encodeCompressed = function encodeCompressed(enc) {\n  return this.encode(enc, true);\n};\n\nBasePoint.prototype._encode = function _encode(compact) {\n  var len = this.curve.p.byteLength();\n  var x = this.getX().toArray('be', len);\n\n  if (compact)\n    return [ this.getY().isEven() ? 0x02 : 0x03 ].concat(x);\n\n  return [ 0x04 ].concat(x, this.getY().toArray('be', len));\n};\n\nBasePoint.prototype.encode = function encode(enc, compact) {\n  return utils.encode(this._encode(compact), enc);\n};\n\nBasePoint.prototype.precompute = function precompute(power) {\n  if (this.precomputed)\n    return this;\n\n  var precomputed = {\n    doubles: null,\n    naf: null,\n    beta: null,\n  };\n  precomputed.naf = this._getNAFPoints(8);\n  precomputed.doubles = this._getDoubles(4, power);\n  precomputed.beta = this._getBeta();\n  this.precomputed = precomputed;\n\n  return this;\n};\n\nBasePoint.prototype._hasDoubles = function _hasDoubles(k) {\n  if (!this.precomputed)\n    return false;\n\n  var doubles = this.precomputed.doubles;\n  if (!doubles)\n    return false;\n\n  return doubles.points.length >= Math.ceil((k.bitLength() + 1) / doubles.step);\n};\n\nBasePoint.prototype._getDoubles = function _getDoubles(step, power) {\n  if (this.precomputed && this.precomputed.doubles)\n    return this.precomputed.doubles;\n\n  var doubles = [ this ];\n  var acc = this;\n  for (var i = 0; i < power; i += step) {\n    for (var j = 0; j < step; j++)\n      acc = acc.dbl();\n    doubles.push(acc);\n  }\n  return {\n    step: step,\n    points: doubles,\n  };\n};\n\nBasePoint.prototype._getNAFPoints = function _getNAFPoints(wnd) {\n  if (this.precomputed && this.precomputed.naf)\n    return this.precomputed.naf;\n\n  var res = [ this ];\n  var max = (1 << wnd) - 1;\n  var dbl = max === 1 ? null : this.dbl();\n  for (var i = 1; i < max; i++)\n    res[i] = res[i - 1].add(dbl);\n  return {\n    wnd: wnd,\n    points: res,\n  };\n};\n\nBasePoint.prototype._getBeta = function _getBeta() {\n  return null;\n};\n\nBasePoint.prototype.dblp = function dblp(k) {\n  var r = this;\n  for (var i = 0; i < k; i++)\n    r = r.dbl();\n  return r;\n};\n", "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n", "'use strict';\n\nvar utils = require('../utils');\nvar BN = require('bn.js');\nvar inherits = require('inherits');\nvar Base = require('./base');\n\nvar assert = utils.assert;\n\nfunction ShortCurve(conf) {\n  Base.call(this, 'short', conf);\n\n  this.a = new BN(conf.a, 16).toRed(this.red);\n  this.b = new BN(conf.b, 16).toRed(this.red);\n  this.tinv = this.two.redInvm();\n\n  this.zeroA = this.a.fromRed().cmpn(0) === 0;\n  this.threeA = this.a.fromRed().sub(this.p).cmpn(-3) === 0;\n\n  // If the curve is endomorphic, precalculate beta and lambda\n  this.endo = this._getEndomorphism(conf);\n  this._endoWnafT1 = new Array(4);\n  this._endoWnafT2 = new Array(4);\n}\ninherits(ShortCurve, Base);\nmodule.exports = ShortCurve;\n\nShortCurve.prototype._getEndomorphism = function _getEndomorphism(conf) {\n  // No efficient endomorphism\n  if (!this.zeroA || !this.g || !this.n || this.p.modn(3) !== 1)\n    return;\n\n  // Compute beta and lambda, that lambda * P = (beta * Px; Py)\n  var beta;\n  var lambda;\n  if (conf.beta) {\n    beta = new BN(conf.beta, 16).toRed(this.red);\n  } else {\n    var betas = this._getEndoRoots(this.p);\n    // Choose the smallest beta\n    beta = betas[0].cmp(betas[1]) < 0 ? betas[0] : betas[1];\n    beta = beta.toRed(this.red);\n  }\n  if (conf.lambda) {\n    lambda = new BN(conf.lambda, 16);\n  } else {\n    // Choose the lambda that is matching selected beta\n    var lambdas = this._getEndoRoots(this.n);\n    if (this.g.mul(lambdas[0]).x.cmp(this.g.x.redMul(beta)) === 0) {\n      lambda = lambdas[0];\n    } else {\n      lambda = lambdas[1];\n      assert(this.g.mul(lambda).x.cmp(this.g.x.redMul(beta)) === 0);\n    }\n  }\n\n  // Get basis vectors, used for balanced length-two representation\n  var basis;\n  if (conf.basis) {\n    basis = conf.basis.map(function(vec) {\n      return {\n        a: new BN(vec.a, 16),\n        b: new BN(vec.b, 16),\n      };\n    });\n  } else {\n    basis = this._getEndoBasis(lambda);\n  }\n\n  return {\n    beta: beta,\n    lambda: lambda,\n    basis: basis,\n  };\n};\n\nShortCurve.prototype._getEndoRoots = function _getEndoRoots(num) {\n  // Find roots of for x^2 + x + 1 in F\n  // Root = (-1 +- Sqrt(-3)) / 2\n  //\n  var red = num === this.p ? this.red : BN.mont(num);\n  var tinv = new BN(2).toRed(red).redInvm();\n  var ntinv = tinv.redNeg();\n\n  var s = new BN(3).toRed(red).redNeg().redSqrt().redMul(tinv);\n\n  var l1 = ntinv.redAdd(s).fromRed();\n  var l2 = ntinv.redSub(s).fromRed();\n  return [ l1, l2 ];\n};\n\nShortCurve.prototype._getEndoBasis = function _getEndoBasis(lambda) {\n  // aprxSqrt >= sqrt(this.n)\n  var aprxSqrt = this.n.ushrn(Math.floor(this.n.bitLength() / 2));\n\n  // 3.74\n  // Run EGCD, until r(L + 1) < aprxSqrt\n  var u = lambda;\n  var v = this.n.clone();\n  var x1 = new BN(1);\n  var y1 = new BN(0);\n  var x2 = new BN(0);\n  var y2 = new BN(1);\n\n  // NOTE: all vectors are roots of: a + b * lambda = 0 (mod n)\n  var a0;\n  var b0;\n  // First vector\n  var a1;\n  var b1;\n  // Second vector\n  var a2;\n  var b2;\n\n  var prevR;\n  var i = 0;\n  var r;\n  var x;\n  while (u.cmpn(0) !== 0) {\n    var q = v.div(u);\n    r = v.sub(q.mul(u));\n    x = x2.sub(q.mul(x1));\n    var y = y2.sub(q.mul(y1));\n\n    if (!a1 && r.cmp(aprxSqrt) < 0) {\n      a0 = prevR.neg();\n      b0 = x1;\n      a1 = r.neg();\n      b1 = x;\n    } else if (a1 && ++i === 2) {\n      break;\n    }\n    prevR = r;\n\n    v = u;\n    u = r;\n    x2 = x1;\n    x1 = x;\n    y2 = y1;\n    y1 = y;\n  }\n  a2 = r.neg();\n  b2 = x;\n\n  var len1 = a1.sqr().add(b1.sqr());\n  var len2 = a2.sqr().add(b2.sqr());\n  if (len2.cmp(len1) >= 0) {\n    a2 = a0;\n    b2 = b0;\n  }\n\n  // Normalize signs\n  if (a1.negative) {\n    a1 = a1.neg();\n    b1 = b1.neg();\n  }\n  if (a2.negative) {\n    a2 = a2.neg();\n    b2 = b2.neg();\n  }\n\n  return [\n    { a: a1, b: b1 },\n    { a: a2, b: b2 },\n  ];\n};\n\nShortCurve.prototype._endoSplit = function _endoSplit(k) {\n  var basis = this.endo.basis;\n  var v1 = basis[0];\n  var v2 = basis[1];\n\n  var c1 = v2.b.mul(k).divRound(this.n);\n  var c2 = v1.b.neg().mul(k).divRound(this.n);\n\n  var p1 = c1.mul(v1.a);\n  var p2 = c2.mul(v2.a);\n  var q1 = c1.mul(v1.b);\n  var q2 = c2.mul(v2.b);\n\n  // Calculate answer\n  var k1 = k.sub(p1).sub(p2);\n  var k2 = q1.add(q2).neg();\n  return { k1: k1, k2: k2 };\n};\n\nShortCurve.prototype.pointFromX = function pointFromX(x, odd) {\n  x = new BN(x, 16);\n  if (!x.red)\n    x = x.toRed(this.red);\n\n  var y2 = x.redSqr().redMul(x).redIAdd(x.redMul(this.a)).redIAdd(this.b);\n  var y = y2.redSqrt();\n  if (y.redSqr().redSub(y2).cmp(this.zero) !== 0)\n    throw new Error('invalid point');\n\n  // XXX Is there any way to tell if the number is odd without converting it\n  // to non-red form?\n  var isOdd = y.fromRed().isOdd();\n  if (odd && !isOdd || !odd && isOdd)\n    y = y.redNeg();\n\n  return this.point(x, y);\n};\n\nShortCurve.prototype.validate = function validate(point) {\n  if (point.inf)\n    return true;\n\n  var x = point.x;\n  var y = point.y;\n\n  var ax = this.a.redMul(x);\n  var rhs = x.redSqr().redMul(x).redIAdd(ax).redIAdd(this.b);\n  return y.redSqr().redISub(rhs).cmpn(0) === 0;\n};\n\nShortCurve.prototype._endoWnafMulAdd =\n    function _endoWnafMulAdd(points, coeffs, jacobianResult) {\n      var npoints = this._endoWnafT1;\n      var ncoeffs = this._endoWnafT2;\n      for (var i = 0; i < points.length; i++) {\n        var split = this._endoSplit(coeffs[i]);\n        var p = points[i];\n        var beta = p._getBeta();\n\n        if (split.k1.negative) {\n          split.k1.ineg();\n          p = p.neg(true);\n        }\n        if (split.k2.negative) {\n          split.k2.ineg();\n          beta = beta.neg(true);\n        }\n\n        npoints[i * 2] = p;\n        npoints[i * 2 + 1] = beta;\n        ncoeffs[i * 2] = split.k1;\n        ncoeffs[i * 2 + 1] = split.k2;\n      }\n      var res = this._wnafMulAdd(1, npoints, ncoeffs, i * 2, jacobianResult);\n\n      // Clean-up references to points and coefficients\n      for (var j = 0; j < i * 2; j++) {\n        npoints[j] = null;\n        ncoeffs[j] = null;\n      }\n      return res;\n    };\n\nfunction Point(curve, x, y, isRed) {\n  Base.BasePoint.call(this, curve, 'affine');\n  if (x === null && y === null) {\n    this.x = null;\n    this.y = null;\n    this.inf = true;\n  } else {\n    this.x = new BN(x, 16);\n    this.y = new BN(y, 16);\n    // Force redgomery representation when loading from JSON\n    if (isRed) {\n      this.x.forceRed(this.curve.red);\n      this.y.forceRed(this.curve.red);\n    }\n    if (!this.x.red)\n      this.x = this.x.toRed(this.curve.red);\n    if (!this.y.red)\n      this.y = this.y.toRed(this.curve.red);\n    this.inf = false;\n  }\n}\ninherits(Point, Base.BasePoint);\n\nShortCurve.prototype.point = function point(x, y, isRed) {\n  return new Point(this, x, y, isRed);\n};\n\nShortCurve.prototype.pointFromJSON = function pointFromJSON(obj, red) {\n  return Point.fromJSON(this, obj, red);\n};\n\nPoint.prototype._getBeta = function _getBeta() {\n  if (!this.curve.endo)\n    return;\n\n  var pre = this.precomputed;\n  if (pre && pre.beta)\n    return pre.beta;\n\n  var beta = this.curve.point(this.x.redMul(this.curve.endo.beta), this.y);\n  if (pre) {\n    var curve = this.curve;\n    var endoMul = function(p) {\n      return curve.point(p.x.redMul(curve.endo.beta), p.y);\n    };\n    pre.beta = beta;\n    beta.precomputed = {\n      beta: null,\n      naf: pre.naf && {\n        wnd: pre.naf.wnd,\n        points: pre.naf.points.map(endoMul),\n      },\n      doubles: pre.doubles && {\n        step: pre.doubles.step,\n        points: pre.doubles.points.map(endoMul),\n      },\n    };\n  }\n  return beta;\n};\n\nPoint.prototype.toJSON = function toJSON() {\n  if (!this.precomputed)\n    return [ this.x, this.y ];\n\n  return [ this.x, this.y, this.precomputed && {\n    doubles: this.precomputed.doubles && {\n      step: this.precomputed.doubles.step,\n      points: this.precomputed.doubles.points.slice(1),\n    },\n    naf: this.precomputed.naf && {\n      wnd: this.precomputed.naf.wnd,\n      points: this.precomputed.naf.points.slice(1),\n    },\n  } ];\n};\n\nPoint.fromJSON = function fromJSON(curve, obj, red) {\n  if (typeof obj === 'string')\n    obj = JSON.parse(obj);\n  var res = curve.point(obj[0], obj[1], red);\n  if (!obj[2])\n    return res;\n\n  function obj2point(obj) {\n    return curve.point(obj[0], obj[1], red);\n  }\n\n  var pre = obj[2];\n  res.precomputed = {\n    beta: null,\n    doubles: pre.doubles && {\n      step: pre.doubles.step,\n      points: [ res ].concat(pre.doubles.points.map(obj2point)),\n    },\n    naf: pre.naf && {\n      wnd: pre.naf.wnd,\n      points: [ res ].concat(pre.naf.points.map(obj2point)),\n    },\n  };\n  return res;\n};\n\nPoint.prototype.inspect = function inspect() {\n  if (this.isInfinity())\n    return '<EC Point Infinity>';\n  return '<EC Point x: ' + this.x.fromRed().toString(16, 2) +\n      ' y: ' + this.y.fromRed().toString(16, 2) + '>';\n};\n\nPoint.prototype.isInfinity = function isInfinity() {\n  return this.inf;\n};\n\nPoint.prototype.add = function add(p) {\n  // O + P = P\n  if (this.inf)\n    return p;\n\n  // P + O = P\n  if (p.inf)\n    return this;\n\n  // P + P = 2P\n  if (this.eq(p))\n    return this.dbl();\n\n  // P + (-P) = O\n  if (this.neg().eq(p))\n    return this.curve.point(null, null);\n\n  // P + Q = O\n  if (this.x.cmp(p.x) === 0)\n    return this.curve.point(null, null);\n\n  var c = this.y.redSub(p.y);\n  if (c.cmpn(0) !== 0)\n    c = c.redMul(this.x.redSub(p.x).redInvm());\n  var nx = c.redSqr().redISub(this.x).redISub(p.x);\n  var ny = c.redMul(this.x.redSub(nx)).redISub(this.y);\n  return this.curve.point(nx, ny);\n};\n\nPoint.prototype.dbl = function dbl() {\n  if (this.inf)\n    return this;\n\n  // 2P = O\n  var ys1 = this.y.redAdd(this.y);\n  if (ys1.cmpn(0) === 0)\n    return this.curve.point(null, null);\n\n  var a = this.curve.a;\n\n  var x2 = this.x.redSqr();\n  var dyinv = ys1.redInvm();\n  var c = x2.redAdd(x2).redIAdd(x2).redIAdd(a).redMul(dyinv);\n\n  var nx = c.redSqr().redISub(this.x.redAdd(this.x));\n  var ny = c.redMul(this.x.redSub(nx)).redISub(this.y);\n  return this.curve.point(nx, ny);\n};\n\nPoint.prototype.getX = function getX() {\n  return this.x.fromRed();\n};\n\nPoint.prototype.getY = function getY() {\n  return this.y.fromRed();\n};\n\nPoint.prototype.mul = function mul(k) {\n  k = new BN(k, 16);\n  if (this.isInfinity())\n    return this;\n  else if (this._hasDoubles(k))\n    return this.curve._fixedNafMul(this, k);\n  else if (this.curve.endo)\n    return this.curve._endoWnafMulAdd([ this ], [ k ]);\n  else\n    return this.curve._wnafMul(this, k);\n};\n\nPoint.prototype.mulAdd = function mulAdd(k1, p2, k2) {\n  var points = [ this, p2 ];\n  var coeffs = [ k1, k2 ];\n  if (this.curve.endo)\n    return this.curve._endoWnafMulAdd(points, coeffs);\n  else\n    return this.curve._wnafMulAdd(1, points, coeffs, 2);\n};\n\nPoint.prototype.jmulAdd = function jmulAdd(k1, p2, k2) {\n  var points = [ this, p2 ];\n  var coeffs = [ k1, k2 ];\n  if (this.curve.endo)\n    return this.curve._endoWnafMulAdd(points, coeffs, true);\n  else\n    return this.curve._wnafMulAdd(1, points, coeffs, 2, true);\n};\n\nPoint.prototype.eq = function eq(p) {\n  return this === p ||\n         this.inf === p.inf &&\n             (this.inf || this.x.cmp(p.x) === 0 && this.y.cmp(p.y) === 0);\n};\n\nPoint.prototype.neg = function neg(_precompute) {\n  if (this.inf)\n    return this;\n\n  var res = this.curve.point(this.x, this.y.redNeg());\n  if (_precompute && this.precomputed) {\n    var pre = this.precomputed;\n    var negate = function(p) {\n      return p.neg();\n    };\n    res.precomputed = {\n      naf: pre.naf && {\n        wnd: pre.naf.wnd,\n        points: pre.naf.points.map(negate),\n      },\n      doubles: pre.doubles && {\n        step: pre.doubles.step,\n        points: pre.doubles.points.map(negate),\n      },\n    };\n  }\n  return res;\n};\n\nPoint.prototype.toJ = function toJ() {\n  if (this.inf)\n    return this.curve.jpoint(null, null, null);\n\n  var res = this.curve.jpoint(this.x, this.y, this.curve.one);\n  return res;\n};\n\nfunction JPoint(curve, x, y, z) {\n  Base.BasePoint.call(this, curve, 'jacobian');\n  if (x === null && y === null && z === null) {\n    this.x = this.curve.one;\n    this.y = this.curve.one;\n    this.z = new BN(0);\n  } else {\n    this.x = new BN(x, 16);\n    this.y = new BN(y, 16);\n    this.z = new BN(z, 16);\n  }\n  if (!this.x.red)\n    this.x = this.x.toRed(this.curve.red);\n  if (!this.y.red)\n    this.y = this.y.toRed(this.curve.red);\n  if (!this.z.red)\n    this.z = this.z.toRed(this.curve.red);\n\n  this.zOne = this.z === this.curve.one;\n}\ninherits(JPoint, Base.BasePoint);\n\nShortCurve.prototype.jpoint = function jpoint(x, y, z) {\n  return new JPoint(this, x, y, z);\n};\n\nJPoint.prototype.toP = function toP() {\n  if (this.isInfinity())\n    return this.curve.point(null, null);\n\n  var zinv = this.z.redInvm();\n  var zinv2 = zinv.redSqr();\n  var ax = this.x.redMul(zinv2);\n  var ay = this.y.redMul(zinv2).redMul(zinv);\n\n  return this.curve.point(ax, ay);\n};\n\nJPoint.prototype.neg = function neg() {\n  return this.curve.jpoint(this.x, this.y.redNeg(), this.z);\n};\n\nJPoint.prototype.add = function add(p) {\n  // O + P = P\n  if (this.isInfinity())\n    return p;\n\n  // P + O = P\n  if (p.isInfinity())\n    return this;\n\n  // 12M + 4S + 7A\n  var pz2 = p.z.redSqr();\n  var z2 = this.z.redSqr();\n  var u1 = this.x.redMul(pz2);\n  var u2 = p.x.redMul(z2);\n  var s1 = this.y.redMul(pz2.redMul(p.z));\n  var s2 = p.y.redMul(z2.redMul(this.z));\n\n  var h = u1.redSub(u2);\n  var r = s1.redSub(s2);\n  if (h.cmpn(0) === 0) {\n    if (r.cmpn(0) !== 0)\n      return this.curve.jpoint(null, null, null);\n    else\n      return this.dbl();\n  }\n\n  var h2 = h.redSqr();\n  var h3 = h2.redMul(h);\n  var v = u1.redMul(h2);\n\n  var nx = r.redSqr().redIAdd(h3).redISub(v).redISub(v);\n  var ny = r.redMul(v.redISub(nx)).redISub(s1.redMul(h3));\n  var nz = this.z.redMul(p.z).redMul(h);\n\n  return this.curve.jpoint(nx, ny, nz);\n};\n\nJPoint.prototype.mixedAdd = function mixedAdd(p) {\n  // O + P = P\n  if (this.isInfinity())\n    return p.toJ();\n\n  // P + O = P\n  if (p.isInfinity())\n    return this;\n\n  // 8M + 3S + 7A\n  var z2 = this.z.redSqr();\n  var u1 = this.x;\n  var u2 = p.x.redMul(z2);\n  var s1 = this.y;\n  var s2 = p.y.redMul(z2).redMul(this.z);\n\n  var h = u1.redSub(u2);\n  var r = s1.redSub(s2);\n  if (h.cmpn(0) === 0) {\n    if (r.cmpn(0) !== 0)\n      return this.curve.jpoint(null, null, null);\n    else\n      return this.dbl();\n  }\n\n  var h2 = h.redSqr();\n  var h3 = h2.redMul(h);\n  var v = u1.redMul(h2);\n\n  var nx = r.redSqr().redIAdd(h3).redISub(v).redISub(v);\n  var ny = r.redMul(v.redISub(nx)).redISub(s1.redMul(h3));\n  var nz = this.z.redMul(h);\n\n  return this.curve.jpoint(nx, ny, nz);\n};\n\nJPoint.prototype.dblp = function dblp(pow) {\n  if (pow === 0)\n    return this;\n  if (this.isInfinity())\n    return this;\n  if (!pow)\n    return this.dbl();\n\n  var i;\n  if (this.curve.zeroA || this.curve.threeA) {\n    var r = this;\n    for (i = 0; i < pow; i++)\n      r = r.dbl();\n    return r;\n  }\n\n  // 1M + 2S + 1A + N * (4S + 5M + 8A)\n  // N = 1 => 6M + 6S + 9A\n  var a = this.curve.a;\n  var tinv = this.curve.tinv;\n\n  var jx = this.x;\n  var jy = this.y;\n  var jz = this.z;\n  var jz4 = jz.redSqr().redSqr();\n\n  // Reuse results\n  var jyd = jy.redAdd(jy);\n  for (i = 0; i < pow; i++) {\n    var jx2 = jx.redSqr();\n    var jyd2 = jyd.redSqr();\n    var jyd4 = jyd2.redSqr();\n    var c = jx2.redAdd(jx2).redIAdd(jx2).redIAdd(a.redMul(jz4));\n\n    var t1 = jx.redMul(jyd2);\n    var nx = c.redSqr().redISub(t1.redAdd(t1));\n    var t2 = t1.redISub(nx);\n    var dny = c.redMul(t2);\n    dny = dny.redIAdd(dny).redISub(jyd4);\n    var nz = jyd.redMul(jz);\n    if (i + 1 < pow)\n      jz4 = jz4.redMul(jyd4);\n\n    jx = nx;\n    jz = nz;\n    jyd = dny;\n  }\n\n  return this.curve.jpoint(jx, jyd.redMul(tinv), jz);\n};\n\nJPoint.prototype.dbl = function dbl() {\n  if (this.isInfinity())\n    return this;\n\n  if (this.curve.zeroA)\n    return this._zeroDbl();\n  else if (this.curve.threeA)\n    return this._threeDbl();\n  else\n    return this._dbl();\n};\n\nJPoint.prototype._zeroDbl = function _zeroDbl() {\n  var nx;\n  var ny;\n  var nz;\n  // Z = 1\n  if (this.zOne) {\n    // hyperelliptic.org/EFD/g1p/auto-shortw-jacobian-0.html\n    //     #doubling-mdbl-2007-bl\n    // 1M + 5S + 14A\n\n    // XX = X1^2\n    var xx = this.x.redSqr();\n    // YY = Y1^2\n    var yy = this.y.redSqr();\n    // YYYY = YY^2\n    var yyyy = yy.redSqr();\n    // S = 2 * ((X1 + YY)^2 - XX - YYYY)\n    var s = this.x.redAdd(yy).redSqr().redISub(xx).redISub(yyyy);\n    s = s.redIAdd(s);\n    // M = 3 * XX + a; a = 0\n    var m = xx.redAdd(xx).redIAdd(xx);\n    // T = M ^ 2 - 2*S\n    var t = m.redSqr().redISub(s).redISub(s);\n\n    // 8 * YYYY\n    var yyyy8 = yyyy.redIAdd(yyyy);\n    yyyy8 = yyyy8.redIAdd(yyyy8);\n    yyyy8 = yyyy8.redIAdd(yyyy8);\n\n    // X3 = T\n    nx = t;\n    // Y3 = M * (S - T) - 8 * YYYY\n    ny = m.redMul(s.redISub(t)).redISub(yyyy8);\n    // Z3 = 2*Y1\n    nz = this.y.redAdd(this.y);\n  } else {\n    // hyperelliptic.org/EFD/g1p/auto-shortw-jacobian-0.html\n    //     #doubling-dbl-2009-l\n    // 2M + 5S + 13A\n\n    // A = X1^2\n    var a = this.x.redSqr();\n    // B = Y1^2\n    var b = this.y.redSqr();\n    // C = B^2\n    var c = b.redSqr();\n    // D = 2 * ((X1 + B)^2 - A - C)\n    var d = this.x.redAdd(b).redSqr().redISub(a).redISub(c);\n    d = d.redIAdd(d);\n    // E = 3 * A\n    var e = a.redAdd(a).redIAdd(a);\n    // F = E^2\n    var f = e.redSqr();\n\n    // 8 * C\n    var c8 = c.redIAdd(c);\n    c8 = c8.redIAdd(c8);\n    c8 = c8.redIAdd(c8);\n\n    // X3 = F - 2 * D\n    nx = f.redISub(d).redISub(d);\n    // Y3 = E * (D - X3) - 8 * C\n    ny = e.redMul(d.redISub(nx)).redISub(c8);\n    // Z3 = 2 * Y1 * Z1\n    nz = this.y.redMul(this.z);\n    nz = nz.redIAdd(nz);\n  }\n\n  return this.curve.jpoint(nx, ny, nz);\n};\n\nJPoint.prototype._threeDbl = function _threeDbl() {\n  var nx;\n  var ny;\n  var nz;\n  // Z = 1\n  if (this.zOne) {\n    // hyperelliptic.org/EFD/g1p/auto-shortw-jacobian-3.html\n    //     #doubling-mdbl-2007-bl\n    // 1M + 5S + 15A\n\n    // XX = X1^2\n    var xx = this.x.redSqr();\n    // YY = Y1^2\n    var yy = this.y.redSqr();\n    // YYYY = YY^2\n    var yyyy = yy.redSqr();\n    // S = 2 * ((X1 + YY)^2 - XX - YYYY)\n    var s = this.x.redAdd(yy).redSqr().redISub(xx).redISub(yyyy);\n    s = s.redIAdd(s);\n    // M = 3 * XX + a\n    var m = xx.redAdd(xx).redIAdd(xx).redIAdd(this.curve.a);\n    // T = M^2 - 2 * S\n    var t = m.redSqr().redISub(s).redISub(s);\n    // X3 = T\n    nx = t;\n    // Y3 = M * (S - T) - 8 * YYYY\n    var yyyy8 = yyyy.redIAdd(yyyy);\n    yyyy8 = yyyy8.redIAdd(yyyy8);\n    yyyy8 = yyyy8.redIAdd(yyyy8);\n    ny = m.redMul(s.redISub(t)).redISub(yyyy8);\n    // Z3 = 2 * Y1\n    nz = this.y.redAdd(this.y);\n  } else {\n    // hyperelliptic.org/EFD/g1p/auto-shortw-jacobian-3.html#doubling-dbl-2001-b\n    // 3M + 5S\n\n    // delta = Z1^2\n    var delta = this.z.redSqr();\n    // gamma = Y1^2\n    var gamma = this.y.redSqr();\n    // beta = X1 * gamma\n    var beta = this.x.redMul(gamma);\n    // alpha = 3 * (X1 - delta) * (X1 + delta)\n    var alpha = this.x.redSub(delta).redMul(this.x.redAdd(delta));\n    alpha = alpha.redAdd(alpha).redIAdd(alpha);\n    // X3 = alpha^2 - 8 * beta\n    var beta4 = beta.redIAdd(beta);\n    beta4 = beta4.redIAdd(beta4);\n    var beta8 = beta4.redAdd(beta4);\n    nx = alpha.redSqr().redISub(beta8);\n    // Z3 = (Y1 + Z1)^2 - gamma - delta\n    nz = this.y.redAdd(this.z).redSqr().redISub(gamma).redISub(delta);\n    // Y3 = alpha * (4 * beta - X3) - 8 * gamma^2\n    var ggamma8 = gamma.redSqr();\n    ggamma8 = ggamma8.redIAdd(ggamma8);\n    ggamma8 = ggamma8.redIAdd(ggamma8);\n    ggamma8 = ggamma8.redIAdd(ggamma8);\n    ny = alpha.redMul(beta4.redISub(nx)).redISub(ggamma8);\n  }\n\n  return this.curve.jpoint(nx, ny, nz);\n};\n\nJPoint.prototype._dbl = function _dbl() {\n  var a = this.curve.a;\n\n  // 4M + 6S + 10A\n  var jx = this.x;\n  var jy = this.y;\n  var jz = this.z;\n  var jz4 = jz.redSqr().redSqr();\n\n  var jx2 = jx.redSqr();\n  var jy2 = jy.redSqr();\n\n  var c = jx2.redAdd(jx2).redIAdd(jx2).redIAdd(a.redMul(jz4));\n\n  var jxd4 = jx.redAdd(jx);\n  jxd4 = jxd4.redIAdd(jxd4);\n  var t1 = jxd4.redMul(jy2);\n  var nx = c.redSqr().redISub(t1.redAdd(t1));\n  var t2 = t1.redISub(nx);\n\n  var jyd8 = jy2.redSqr();\n  jyd8 = jyd8.redIAdd(jyd8);\n  jyd8 = jyd8.redIAdd(jyd8);\n  jyd8 = jyd8.redIAdd(jyd8);\n  var ny = c.redMul(t2).redISub(jyd8);\n  var nz = jy.redAdd(jy).redMul(jz);\n\n  return this.curve.jpoint(nx, ny, nz);\n};\n\nJPoint.prototype.trpl = function trpl() {\n  if (!this.curve.zeroA)\n    return this.dbl().add(this);\n\n  // hyperelliptic.org/EFD/g1p/auto-shortw-jacobian-0.html#tripling-tpl-2007-bl\n  // 5M + 10S + ...\n\n  // XX = X1^2\n  var xx = this.x.redSqr();\n  // YY = Y1^2\n  var yy = this.y.redSqr();\n  // ZZ = Z1^2\n  var zz = this.z.redSqr();\n  // YYYY = YY^2\n  var yyyy = yy.redSqr();\n  // M = 3 * XX + a * ZZ2; a = 0\n  var m = xx.redAdd(xx).redIAdd(xx);\n  // MM = M^2\n  var mm = m.redSqr();\n  // E = 6 * ((X1 + YY)^2 - XX - YYYY) - MM\n  var e = this.x.redAdd(yy).redSqr().redISub(xx).redISub(yyyy);\n  e = e.redIAdd(e);\n  e = e.redAdd(e).redIAdd(e);\n  e = e.redISub(mm);\n  // EE = E^2\n  var ee = e.redSqr();\n  // T = 16*YYYY\n  var t = yyyy.redIAdd(yyyy);\n  t = t.redIAdd(t);\n  t = t.redIAdd(t);\n  t = t.redIAdd(t);\n  // U = (M + E)^2 - MM - EE - T\n  var u = m.redIAdd(e).redSqr().redISub(mm).redISub(ee).redISub(t);\n  // X3 = 4 * (X1 * EE - 4 * YY * U)\n  var yyu4 = yy.redMul(u);\n  yyu4 = yyu4.redIAdd(yyu4);\n  yyu4 = yyu4.redIAdd(yyu4);\n  var nx = this.x.redMul(ee).redISub(yyu4);\n  nx = nx.redIAdd(nx);\n  nx = nx.redIAdd(nx);\n  // Y3 = 8 * Y1 * (U * (T - U) - E * EE)\n  var ny = this.y.redMul(u.redMul(t.redISub(u)).redISub(e.redMul(ee)));\n  ny = ny.redIAdd(ny);\n  ny = ny.redIAdd(ny);\n  ny = ny.redIAdd(ny);\n  // Z3 = (Z1 + E)^2 - ZZ - EE\n  var nz = this.z.redAdd(e).redSqr().redISub(zz).redISub(ee);\n\n  return this.curve.jpoint(nx, ny, nz);\n};\n\nJPoint.prototype.mul = function mul(k, kbase) {\n  k = new BN(k, kbase);\n\n  return this.curve._wnafMul(this, k);\n};\n\nJPoint.prototype.eq = function eq(p) {\n  if (p.type === 'affine')\n    return this.eq(p.toJ());\n\n  if (this === p)\n    return true;\n\n  // x1 * z2^2 == x2 * z1^2\n  var z2 = this.z.redSqr();\n  var pz2 = p.z.redSqr();\n  if (this.x.redMul(pz2).redISub(p.x.redMul(z2)).cmpn(0) !== 0)\n    return false;\n\n  // y1 * z2^3 == y2 * z1^3\n  var z3 = z2.redMul(this.z);\n  var pz3 = pz2.redMul(p.z);\n  return this.y.redMul(pz3).redISub(p.y.redMul(z3)).cmpn(0) === 0;\n};\n\nJPoint.prototype.eqXToP = function eqXToP(x) {\n  var zs = this.z.redSqr();\n  var rx = x.toRed(this.curve.red).redMul(zs);\n  if (this.x.cmp(rx) === 0)\n    return true;\n\n  var xc = x.clone();\n  var t = this.curve.redN.redMul(zs);\n  for (;;) {\n    xc.iadd(this.curve.n);\n    if (xc.cmp(this.curve.p) >= 0)\n      return false;\n\n    rx.redIAdd(t);\n    if (this.x.cmp(rx) === 0)\n      return true;\n  }\n};\n\nJPoint.prototype.inspect = function inspect() {\n  if (this.isInfinity())\n    return '<EC JPoint Infinity>';\n  return '<EC JPoint x: ' + this.x.toString(16, 2) +\n      ' y: ' + this.y.toString(16, 2) +\n      ' z: ' + this.z.toString(16, 2) + '>';\n};\n\nJPoint.prototype.isInfinity = function isInfinity() {\n  // XXX This code assumes that zero is always zero in red\n  return this.z.cmpn(0) === 0;\n};\n", "'use strict';\n\nvar curve = exports;\n\ncurve.base = require('./base');\ncurve.short = require('./short');\ncurve.mont = require('./mont');\ncurve.edwards = require('./edwards');\n", "'use strict';\n\nvar curves = exports;\n\nvar hash = require('hash.js');\nvar curve = require('./curve');\nvar utils = require('./utils');\n\nvar assert = utils.assert;\n\nfunction PresetCurve(options) {\n  if (options.type === 'short')\n    this.curve = new curve.short(options);\n  else if (options.type === 'edwards')\n    this.curve = new curve.edwards(options);\n  else\n    this.curve = new curve.mont(options);\n  this.g = this.curve.g;\n  this.n = this.curve.n;\n  this.hash = options.hash;\n\n  assert(this.g.validate(), 'Invalid curve');\n  assert(this.g.mul(this.n).isInfinity(), 'Invalid curve, G*N != O');\n}\ncurves.PresetCurve = PresetCurve;\n\nfunction defineCurve(name, options) {\n  Object.defineProperty(curves, name, {\n    configurable: true,\n    enumerable: true,\n    get: function() {\n      var curve = new PresetCurve(options);\n      Object.defineProperty(curves, name, {\n        configurable: true,\n        enumerable: true,\n        value: curve,\n      });\n      return curve;\n    },\n  });\n}\n\ndefineCurve('p192', {\n  type: 'short',\n  prime: 'p192',\n  p: 'ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff',\n  a: 'ffffffff ffffffff ffffffff fffffffe ffffffff fffffffc',\n  b: '64210519 e59c80e7 0fa7e9ab 72243049 feb8deec c146b9b1',\n  n: 'ffffffff ffffffff ffffffff 99def836 146bc9b1 b4d22831',\n  hash: hash.sha256,\n  gRed: false,\n  g: [\n    '188da80e b03090f6 7cbf20eb 43a18800 f4ff0afd 82ff1012',\n    '07192b95 ffc8da78 631011ed 6b24cdd5 73f977a1 1e794811',\n  ],\n});\n\ndefineCurve('p224', {\n  type: 'short',\n  prime: 'p224',\n  p: 'ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001',\n  a: 'ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff fffffffe',\n  b: 'b4050a85 0c04b3ab f5413256 5044b0b7 d7bfd8ba 270b3943 2355ffb4',\n  n: 'ffffffff ffffffff ffffffff ffff16a2 e0b8f03e 13dd2945 5c5c2a3d',\n  hash: hash.sha256,\n  gRed: false,\n  g: [\n    'b70e0cbd 6bb4bf7f 321390b9 4a03c1d3 56c21122 343280d6 115c1d21',\n    'bd376388 b5f723fb 4c22dfe6 cd4375a0 5a074764 44d58199 85007e34',\n  ],\n});\n\ndefineCurve('p256', {\n  type: 'short',\n  prime: null,\n  p: 'ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff ffffffff',\n  a: 'ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff fffffffc',\n  b: '5ac635d8 aa3a93e7 b3ebbd55 769886bc 651d06b0 cc53b0f6 3bce3c3e 27d2604b',\n  n: 'ffffffff 00000000 ffffffff ffffffff bce6faad a7179e84 f3b9cac2 fc632551',\n  hash: hash.sha256,\n  gRed: false,\n  g: [\n    '6b17d1f2 e12c4247 f8bce6e5 63a440f2 77037d81 2deb33a0 f4a13945 d898c296',\n    '4fe342e2 fe1a7f9b 8ee7eb4a 7c0f9e16 2bce3357 6b315ece cbb64068 37bf51f5',\n  ],\n});\n\ndefineCurve('p384', {\n  type: 'short',\n  prime: null,\n  p: 'ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ' +\n     'fffffffe ffffffff 00000000 00000000 ffffffff',\n  a: 'ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ' +\n     'fffffffe ffffffff 00000000 00000000 fffffffc',\n  b: 'b3312fa7 e23ee7e4 988e056b e3f82d19 181d9c6e fe814112 0314088f ' +\n     '5013875a c656398d 8a2ed19d 2a85c8ed d3ec2aef',\n  n: 'ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff c7634d81 ' +\n     'f4372ddf 581a0db2 48b0a77a ecec196a ccc52973',\n  hash: hash.sha384,\n  gRed: false,\n  g: [\n    'aa87ca22 be8b0537 8eb1c71e f320ad74 6e1d3b62 8ba79b98 59f741e0 82542a38 ' +\n    '5502f25d bf55296c 3a545e38 72760ab7',\n    '3617de4a 96262c6f 5d9e98bf 9292dc29 f8f41dbd 289a147c e9da3113 b5f0b8c0 ' +\n    '0a60b1ce 1d7e819d 7a431d7c 90ea0e5f',\n  ],\n});\n\ndefineCurve('p521', {\n  type: 'short',\n  prime: null,\n  p: '000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ' +\n     'ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ' +\n     'ffffffff ffffffff ffffffff ffffffff ffffffff',\n  a: '000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ' +\n     'ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ' +\n     'ffffffff ffffffff ffffffff ffffffff fffffffc',\n  b: '00000051 953eb961 8e1c9a1f 929a21a0 b68540ee a2da725b ' +\n     '99b315f3 b8b48991 8ef109e1 56193951 ec7e937b 1652c0bd ' +\n     '3bb1bf07 3573df88 3d2c34f1 ef451fd4 6b503f00',\n  n: '000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ' +\n     'ffffffff ffffffff fffffffa 51868783 bf2f966b 7fcc0148 ' +\n     'f709a5d0 3bb5c9b8 899c47ae bb6fb71e 91386409',\n  hash: hash.sha512,\n  gRed: false,\n  g: [\n    '000000c6 858e06b7 0404e9cd 9e3ecb66 2395b442 9c648139 ' +\n    '053fb521 f828af60 6b4d3dba a14b5e77 efe75928 fe1dc127 ' +\n    'a2ffa8de 3348b3c1 856a429b f97e7e31 c2e5bd66',\n    '00000118 39296a78 9a3bc004 5c8a5fb4 2c7d1bd9 98f54449 ' +\n    '579b4468 17afbd17 273e662c 97ee7299 5ef42640 c550b901 ' +\n    '3fad0761 353c7086 a272c240 88be9476 9fd16650',\n  ],\n});\n\ndefineCurve('curve25519', {\n  type: 'mont',\n  prime: 'p25519',\n  p: '7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed',\n  a: '76d06',\n  b: '1',\n  n: '1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed',\n  hash: hash.sha256,\n  gRed: false,\n  g: [\n    '9',\n  ],\n});\n\ndefineCurve('ed25519', {\n  type: 'edwards',\n  prime: 'p25519',\n  p: '7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed',\n  a: '-1',\n  c: '1',\n  // -121665 * (121666^(-1)) (mod P)\n  d: '52036cee2b6ffe73 8cc740797779e898 00700a4d4141d8ab 75eb4dca135978a3',\n  n: '1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed',\n  hash: hash.sha256,\n  gRed: false,\n  g: [\n    '216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a',\n\n    // 4/5\n    '6666666666666666666666666666666666666666666666666666666666666658',\n  ],\n});\n\nvar pre;\ntry {\n  pre = require('./precomputed/secp256k1');\n} catch (e) {\n  pre = undefined;\n}\n\ndefineCurve('secp256k1', {\n  type: 'short',\n  prime: 'k256',\n  p: 'ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f',\n  a: '0',\n  b: '7',\n  n: 'ffffffff ffffffff ffffffff fffffffe baaedce6 af48a03b bfd25e8c d0364141',\n  h: '1',\n  hash: hash.sha256,\n\n  // Precomputed endomorphism\n  beta: '7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee',\n  lambda: '5363ad4cc05c30e0a5261c028812645a122e22ea20816678df02967c1b23bd72',\n  basis: [\n    {\n      a: '3086d221a7d46bcde86c90e49284eb15',\n      b: '-e4437ed6010e88286f547fa90abfe4c3',\n    },\n    {\n      a: '114ca50f7a8e2f3f657c1108d9d44cfd8',\n      b: '3086d221a7d46bcde86c90e49284eb15',\n    },\n  ],\n\n  gRed: false,\n  g: [\n    '79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798',\n    '483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8',\n    pre,\n  ],\n});\n", "'use strict';\n\nvar hash = require('hash.js');\nvar utils = require('minimalistic-crypto-utils');\nvar assert = require('minimalistic-assert');\n\nfunction HmacDRBG(options) {\n  if (!(this instanceof HmacDRBG))\n    return new HmacDRBG(options);\n  this.hash = options.hash;\n  this.predResist = !!options.predResist;\n\n  this.outLen = this.hash.outSize;\n  this.minEntropy = options.minEntropy || this.hash.hmacStrength;\n\n  this._reseed = null;\n  this.reseedInterval = null;\n  this.K = null;\n  this.V = null;\n\n  var entropy = utils.toArray(options.entropy, options.entropyEnc || 'hex');\n  var nonce = utils.toArray(options.nonce, options.nonceEnc || 'hex');\n  var pers = utils.toArray(options.pers, options.persEnc || 'hex');\n  assert(entropy.length >= (this.minEntropy / 8),\n         'Not enough entropy. Minimum is: ' + this.minEntropy + ' bits');\n  this._init(entropy, nonce, pers);\n}\nmodule.exports = HmacDRBG;\n\nHmacDRBG.prototype._init = function init(entropy, nonce, pers) {\n  var seed = entropy.concat(nonce).concat(pers);\n\n  this.K = new Array(this.outLen / 8);\n  this.V = new Array(this.outLen / 8);\n  for (var i = 0; i < this.V.length; i++) {\n    this.K[i] = 0x00;\n    this.V[i] = 0x01;\n  }\n\n  this._update(seed);\n  this._reseed = 1;\n  this.reseedInterval = 0x1000000000000;  // 2^48\n};\n\nHmacDRBG.prototype._hmac = function hmac() {\n  return new hash.hmac(this.hash, this.K);\n};\n\nHmacDRBG.prototype._update = function update(seed) {\n  var kmac = this._hmac()\n                 .update(this.V)\n                 .update([ 0x00 ]);\n  if (seed)\n    kmac = kmac.update(seed);\n  this.K = kmac.digest();\n  this.V = this._hmac().update(this.V).digest();\n  if (!seed)\n    return;\n\n  this.K = this._hmac()\n               .update(this.V)\n               .update([ 0x01 ])\n               .update(seed)\n               .digest();\n  this.V = this._hmac().update(this.V).digest();\n};\n\nHmacDRBG.prototype.reseed = function reseed(entropy, entropyEnc, add, addEnc) {\n  // Optional entropy enc\n  if (typeof entropyEnc !== 'string') {\n    addEnc = add;\n    add = entropyEnc;\n    entropyEnc = null;\n  }\n\n  entropy = utils.toArray(entropy, entropyEnc);\n  add = utils.toArray(add, addEnc);\n\n  assert(entropy.length >= (this.minEntropy / 8),\n         'Not enough entropy. Minimum is: ' + this.minEntropy + ' bits');\n\n  this._update(entropy.concat(add || []));\n  this._reseed = 1;\n};\n\nHmacDRBG.prototype.generate = function generate(len, enc, add, addEnc) {\n  if (this._reseed > this.reseedInterval)\n    throw new Error('Reseed is required');\n\n  // Optional encoding\n  if (typeof enc !== 'string') {\n    addEnc = add;\n    add = enc;\n    enc = null;\n  }\n\n  // Optional additional data\n  if (add) {\n    add = utils.toArray(add, addEnc || 'hex');\n    this._update(add);\n  }\n\n  var temp = [];\n  while (temp.length < len) {\n    this.V = this._hmac().update(this.V).digest();\n    temp = temp.concat(this.V);\n  }\n\n  var res = temp.slice(0, len);\n  this._update(add);\n  this._reseed++;\n  return utils.encode(res, enc);\n};\n", "'use strict';\n\nvar BN = require('bn.js');\nvar utils = require('../utils');\nvar assert = utils.assert;\n\nfunction KeyPair(ec, options) {\n  this.ec = ec;\n  this.priv = null;\n  this.pub = null;\n\n  // KeyPair(ec, { priv: ..., pub: ... })\n  if (options.priv)\n    this._importPrivate(options.priv, options.privEnc);\n  if (options.pub)\n    this._importPublic(options.pub, options.pubEnc);\n}\nmodule.exports = KeyPair;\n\nKeyPair.fromPublic = function fromPublic(ec, pub, enc) {\n  if (pub instanceof KeyPair)\n    return pub;\n\n  return new KeyPair(ec, {\n    pub: pub,\n    pubEnc: enc,\n  });\n};\n\nKeyPair.fromPrivate = function fromPrivate(ec, priv, enc) {\n  if (priv instanceof KeyPair)\n    return priv;\n\n  return new KeyPair(ec, {\n    priv: priv,\n    privEnc: enc,\n  });\n};\n\nKeyPair.prototype.validate = function validate() {\n  var pub = this.getPublic();\n\n  if (pub.isInfinity())\n    return { result: false, reason: 'Invalid public key' };\n  if (!pub.validate())\n    return { result: false, reason: 'Public key is not a point' };\n  if (!pub.mul(this.ec.curve.n).isInfinity())\n    return { result: false, reason: 'Public key * N != O' };\n\n  return { result: true, reason: null };\n};\n\nKeyPair.prototype.getPublic = function getPublic(compact, enc) {\n  // compact is optional argument\n  if (typeof compact === 'string') {\n    enc = compact;\n    compact = null;\n  }\n\n  if (!this.pub)\n    this.pub = this.ec.g.mul(this.priv);\n\n  if (!enc)\n    return this.pub;\n\n  return this.pub.encode(enc, compact);\n};\n\nKeyPair.prototype.getPrivate = function getPrivate(enc) {\n  if (enc === 'hex')\n    return this.priv.toString(16, 2);\n  else\n    return this.priv;\n};\n\nKeyPair.prototype._importPrivate = function _importPrivate(key, enc) {\n  this.priv = new BN(key, enc || 16);\n\n  // Ensure that the priv won't be bigger than n, otherwise we may fail\n  // in fixed multiplication method\n  this.priv = this.priv.umod(this.ec.curve.n);\n};\n\nKeyPair.prototype._importPublic = function _importPublic(key, enc) {\n  if (key.x || key.y) {\n    // Montgomery points only have an `x` coordinate.\n    // Weierstrass/Edwards points on the other hand have both `x` and\n    // `y` coordinates.\n    if (this.ec.curve.type === 'mont') {\n      assert(key.x, 'Need x coordinate');\n    } else if (this.ec.curve.type === 'short' ||\n               this.ec.curve.type === 'edwards') {\n      assert(key.x && key.y, 'Need both x and y coordinate');\n    }\n    this.pub = this.ec.curve.point(key.x, key.y);\n    return;\n  }\n  this.pub = this.ec.curve.decodePoint(key, enc);\n};\n\n// ECDH\nKeyPair.prototype.derive = function derive(pub) {\n  if(!pub.validate()) {\n    assert(pub.validate(), 'public point not validated');\n  }\n  return pub.mul(this.priv).getX();\n};\n\n// ECDSA\nKeyPair.prototype.sign = function sign(msg, enc, options) {\n  return this.ec.sign(msg, this, enc, options);\n};\n\nKeyPair.prototype.verify = function verify(msg, signature) {\n  return this.ec.verify(msg, signature, this);\n};\n\nKeyPair.prototype.inspect = function inspect() {\n  return '<Key priv: ' + (this.priv && this.priv.toString(16, 2)) +\n         ' pub: ' + (this.pub && this.pub.inspect()) + ' >';\n};\n", "'use strict';\n\nvar BN = require('bn.js');\n\nvar utils = require('../utils');\nvar assert = utils.assert;\n\nfunction Signature(options, enc) {\n  if (options instanceof Signature)\n    return options;\n\n  if (this._importDER(options, enc))\n    return;\n\n  assert(options.r && options.s, 'Signature without r or s');\n  this.r = new BN(options.r, 16);\n  this.s = new BN(options.s, 16);\n  if (options.recoveryParam === undefined)\n    this.recoveryParam = null;\n  else\n    this.recoveryParam = options.recoveryParam;\n}\nmodule.exports = Signature;\n\nfunction Position() {\n  this.place = 0;\n}\n\nfunction getLength(buf, p) {\n  var initial = buf[p.place++];\n  if (!(initial & 0x80)) {\n    return initial;\n  }\n  var octetLen = initial & 0xf;\n\n  // Indefinite length or overflow\n  if (octetLen === 0 || octetLen > 4) {\n    return false;\n  }\n\n  var val = 0;\n  for (var i = 0, off = p.place; i < octetLen; i++, off++) {\n    val <<= 8;\n    val |= buf[off];\n    val >>>= 0;\n  }\n\n  // Leading zeroes\n  if (val <= 0x7f) {\n    return false;\n  }\n\n  p.place = off;\n  return val;\n}\n\nfunction rmPadding(buf) {\n  var i = 0;\n  var len = buf.length - 1;\n  while (!buf[i] && !(buf[i + 1] & 0x80) && i < len) {\n    i++;\n  }\n  if (i === 0) {\n    return buf;\n  }\n  return buf.slice(i);\n}\n\nSignature.prototype._importDER = function _importDER(data, enc) {\n  data = utils.toArray(data, enc);\n  var p = new Position();\n  if (data[p.place++] !== 0x30) {\n    return false;\n  }\n  var len = getLength(data, p);\n  if (len === false) {\n    return false;\n  }\n  if ((len + p.place) !== data.length) {\n    return false;\n  }\n  if (data[p.place++] !== 0x02) {\n    return false;\n  }\n  var rlen = getLength(data, p);\n  if (rlen === false) {\n    return false;\n  }\n  var r = data.slice(p.place, rlen + p.place);\n  p.place += rlen;\n  if (data[p.place++] !== 0x02) {\n    return false;\n  }\n  var slen = getLength(data, p);\n  if (slen === false) {\n    return false;\n  }\n  if (data.length !== slen + p.place) {\n    return false;\n  }\n  var s = data.slice(p.place, slen + p.place);\n  if (r[0] === 0) {\n    if (r[1] & 0x80) {\n      r = r.slice(1);\n    } else {\n      // Leading zeroes\n      return false;\n    }\n  }\n  if (s[0] === 0) {\n    if (s[1] & 0x80) {\n      s = s.slice(1);\n    } else {\n      // Leading zeroes\n      return false;\n    }\n  }\n\n  this.r = new BN(r);\n  this.s = new BN(s);\n  this.recoveryParam = null;\n\n  return true;\n};\n\nfunction constructLength(arr, len) {\n  if (len < 0x80) {\n    arr.push(len);\n    return;\n  }\n  var octets = 1 + (Math.log(len) / Math.LN2 >>> 3);\n  arr.push(octets | 0x80);\n  while (--octets) {\n    arr.push((len >>> (octets << 3)) & 0xff);\n  }\n  arr.push(len);\n}\n\nSignature.prototype.toDER = function toDER(enc) {\n  var r = this.r.toArray();\n  var s = this.s.toArray();\n\n  // Pad values\n  if (r[0] & 0x80)\n    r = [ 0 ].concat(r);\n  // Pad values\n  if (s[0] & 0x80)\n    s = [ 0 ].concat(s);\n\n  r = rmPadding(r);\n  s = rmPadding(s);\n\n  while (!s[0] && !(s[1] & 0x80)) {\n    s = s.slice(1);\n  }\n  var arr = [ 0x02 ];\n  constructLength(arr, r.length);\n  arr = arr.concat(r);\n  arr.push(0x02);\n  constructLength(arr, s.length);\n  var backHalf = arr.concat(s);\n  var res = [ 0x30 ];\n  constructLength(res, backHalf.length);\n  res = res.concat(backHalf);\n  return utils.encode(res, enc);\n};\n", "'use strict';\n\nvar BN = require('bn.js');\nvar HmacDRBG = require('hmac-drbg');\nvar utils = require('../utils');\nvar curves = require('../curves');\nvar rand = require('brorand');\nvar assert = utils.assert;\n\nvar KeyPair = require('./key');\nvar Signature = require('./signature');\n\nfunction EC(options) {\n  if (!(this instanceof EC))\n    return new EC(options);\n\n  // Shortcut `elliptic.ec(curve-name)`\n  if (typeof options === 'string') {\n    assert(Object.prototype.hasOwnProperty.call(curves, options),\n      'Unknown curve ' + options);\n\n    options = curves[options];\n  }\n\n  // Shortcut for `elliptic.ec(elliptic.curves.curveName)`\n  if (options instanceof curves.PresetCurve)\n    options = { curve: options };\n\n  this.curve = options.curve.curve;\n  this.n = this.curve.n;\n  this.nh = this.n.ushrn(1);\n  this.g = this.curve.g;\n\n  // Point on curve\n  this.g = options.curve.g;\n  this.g.precompute(options.curve.n.bitLength() + 1);\n\n  // Hash for function for DRBG\n  this.hash = options.hash || options.curve.hash;\n}\nmodule.exports = EC;\n\nEC.prototype.keyPair = function keyPair(options) {\n  return new KeyPair(this, options);\n};\n\nEC.prototype.keyFromPrivate = function keyFromPrivate(priv, enc) {\n  return KeyPair.fromPrivate(this, priv, enc);\n};\n\nEC.prototype.keyFromPublic = function keyFromPublic(pub, enc) {\n  return KeyPair.fromPublic(this, pub, enc);\n};\n\nEC.prototype.genKeyPair = function genKeyPair(options) {\n  if (!options)\n    options = {};\n\n  // Instantiate Hmac_DRBG\n  var drbg = new HmacDRBG({\n    hash: this.hash,\n    pers: options.pers,\n    persEnc: options.persEnc || 'utf8',\n    entropy: options.entropy || rand(this.hash.hmacStrength),\n    entropyEnc: options.entropy && options.entropyEnc || 'utf8',\n    nonce: this.n.toArray(),\n  });\n\n  var bytes = this.n.byteLength();\n  var ns2 = this.n.sub(new BN(2));\n  for (;;) {\n    var priv = new BN(drbg.generate(bytes));\n    if (priv.cmp(ns2) > 0)\n      continue;\n\n    priv.iaddn(1);\n    return this.keyFromPrivate(priv);\n  }\n};\n\nEC.prototype._truncateToN = function _truncateToN(msg, truncOnly) {\n  var delta = msg.byteLength() * 8 - this.n.bitLength();\n  if (delta > 0)\n    msg = msg.ushrn(delta);\n  if (!truncOnly && msg.cmp(this.n) >= 0)\n    return msg.sub(this.n);\n  else\n    return msg;\n};\n\nEC.prototype.sign = function sign(msg, key, enc, options) {\n  if (typeof enc === 'object') {\n    options = enc;\n    enc = null;\n  }\n  if (!options)\n    options = {};\n\n  key = this.keyFromPrivate(key, enc);\n  msg = this._truncateToN(new BN(msg, 16));\n\n  // Zero-extend key to provide enough entropy\n  var bytes = this.n.byteLength();\n  var bkey = key.getPrivate().toArray('be', bytes);\n\n  // Zero-extend nonce to have the same byte size as N\n  var nonce = msg.toArray('be', bytes);\n\n  // Instantiate Hmac_DRBG\n  var drbg = new HmacDRBG({\n    hash: this.hash,\n    entropy: bkey,\n    nonce: nonce,\n    pers: options.pers,\n    persEnc: options.persEnc || 'utf8',\n  });\n\n  // Number of bytes to generate\n  var ns1 = this.n.sub(new BN(1));\n\n  for (var iter = 0; ; iter++) {\n    var k = options.k ?\n      options.k(iter) :\n      new BN(drbg.generate(this.n.byteLength()));\n    k = this._truncateToN(k, true);\n    if (k.cmpn(1) <= 0 || k.cmp(ns1) >= 0)\n      continue;\n\n    var kp = this.g.mul(k);\n    if (kp.isInfinity())\n      continue;\n\n    var kpX = kp.getX();\n    var r = kpX.umod(this.n);\n    if (r.cmpn(0) === 0)\n      continue;\n\n    var s = k.invm(this.n).mul(r.mul(key.getPrivate()).iadd(msg));\n    s = s.umod(this.n);\n    if (s.cmpn(0) === 0)\n      continue;\n\n    var recoveryParam = (kp.getY().isOdd() ? 1 : 0) |\n                        (kpX.cmp(r) !== 0 ? 2 : 0);\n\n    // Use complement of `s`, if it is > `n / 2`\n    if (options.canonical && s.cmp(this.nh) > 0) {\n      s = this.n.sub(s);\n      recoveryParam ^= 1;\n    }\n\n    return new Signature({ r: r, s: s, recoveryParam: recoveryParam });\n  }\n};\n\nEC.prototype.verify = function verify(msg, signature, key, enc) {\n  msg = this._truncateToN(new BN(msg, 16));\n  key = this.keyFromPublic(key, enc);\n  signature = new Signature(signature, 'hex');\n\n  // Perform primitive values validation\n  var r = signature.r;\n  var s = signature.s;\n  if (r.cmpn(1) < 0 || r.cmp(this.n) >= 0)\n    return false;\n  if (s.cmpn(1) < 0 || s.cmp(this.n) >= 0)\n    return false;\n\n  // Validate signature\n  var sinv = s.invm(this.n);\n  var u1 = sinv.mul(msg).umod(this.n);\n  var u2 = sinv.mul(r).umod(this.n);\n  var p;\n\n  if (!this.curve._maxwellTrick) {\n    p = this.g.mulAdd(u1, key.getPublic(), u2);\n    if (p.isInfinity())\n      return false;\n\n    return p.getX().umod(this.n).cmp(r) === 0;\n  }\n\n  // NOTE: Greg Maxwell's trick, inspired by:\n  // https://git.io/vad3K\n\n  p = this.g.jmulAdd(u1, key.getPublic(), u2);\n  if (p.isInfinity())\n    return false;\n\n  // Compare `p.x` of Jacobian point with `r`,\n  // this will do `p.x == r * p.z^2` instead of multiplying `p.x` by the\n  // inverse of `p.z^2`\n  return p.eqXToP(r);\n};\n\nEC.prototype.recoverPubKey = function(msg, signature, j, enc) {\n  assert((3 & j) === j, 'The recovery param is more than two bits');\n  signature = new Signature(signature, enc);\n\n  var n = this.n;\n  var e = new BN(msg);\n  var r = signature.r;\n  var s = signature.s;\n\n  // A set LSB signifies that the y-coordinate is odd\n  var isYOdd = j & 1;\n  var isSecondKey = j >> 1;\n  if (r.cmp(this.curve.p.umod(this.curve.n)) >= 0 && isSecondKey)\n    throw new Error('Unable to find sencond key candinate');\n\n  // 1.1. Let x = r + jn.\n  if (isSecondKey)\n    r = this.curve.pointFromX(r.add(this.curve.n), isYOdd);\n  else\n    r = this.curve.pointFromX(r, isYOdd);\n\n  var rInv = signature.r.invm(n);\n  var s1 = n.sub(e).mul(rInv).umod(n);\n  var s2 = s.mul(rInv).umod(n);\n\n  // 1.6.1 Compute Q = r^-1 (sR -  eG)\n  //               Q = r^-1 (sR + -eG)\n  return this.g.mulAdd(s1, r, s2);\n};\n\nEC.prototype.getKeyRecoveryParam = function(e, signature, Q, enc) {\n  signature = new Signature(signature, enc);\n  if (signature.recoveryParam !== null)\n    return signature.recoveryParam;\n\n  for (var i = 0; i < 4; i++) {\n    var Qprime;\n    try {\n      Qprime = this.recoverPubKey(e, signature, i);\n    } catch (e) {\n      continue;\n    }\n\n    if (Qprime.eq(Q))\n      return i;\n  }\n  throw new Error('Unable to find valid recovery factor');\n};\n", "'use strict';\n\nvar elliptic = exports;\n\nelliptic.version = require('../package.json').version;\nelliptic.utils = require('./elliptic/utils');\nelliptic.rand = require('brorand');\nelliptic.curve = require('./elliptic/curve');\nelliptic.curves = require('./elliptic/curves');\n\n// Protocols\nelliptic.ec = require('./elliptic/ec');\nelliptic.eddsa = require('./elliptic/eddsa');\n", "import _ec from \"elliptic\";\nvar EC = _ec.ec;\nexport { EC };\n//# sourceMappingURL=elliptic.js.map"], "names": ["minAssert", "minUtils", "utils", "assert", "Base", "inherits", "require$$0", "require$$1", "curve", "curves", "KeyPair", "HmacDRBG", "Signature", "signature", "require$$2", "require$$3", "EC", "_ec"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sBAAc,GAAG,MAAM,CAAC;AACxB;AACA,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE;AAC1B,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,kBAAkB,CAAC,CAAC;AAC/C,CAAC;AACD;AACA,MAAM,CAAC,KAAK,GAAG,SAAS,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;AAC/C,EAAE,IAAI,CAAC,IAAI,CAAC;AACZ,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,oBAAoB,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC;;;ACVD,YAAY,CAAC;AACb;AACA,IAAI,KAAK,GAAG,OAAO,CAAC;AACpB;AACA,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;AAC3B,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;AACxB,IAAI,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC;AACvB,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;AACvC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,EAAE,IAAI,GAAG,KAAK,KAAK,EAAE;AACrB,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;AAC1C,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC;AAC5B,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC;AAC1C,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAClD,GAAG,MAAM;AACT,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACtB,MAAM,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;AACxB,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACzB;AACA,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AACxB;AACA,SAAS,KAAK,CAAC,IAAI,EAAE;AACrB,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AACvB,IAAI,OAAO,GAAG,GAAG,IAAI,CAAC;AACtB;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACpB;AACA,SAAS,KAAK,CAAC,GAAG,EAAE;AACpB,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;AACrC,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AACtC,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACpB;AACA,KAAK,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE;AACzC,EAAE,IAAI,GAAG,KAAK,KAAK;AACnB,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AACtB;AACA,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;;;;ACzDD,YAAY,CAAC;AACb;AACA,IAAI,KAAK,GAAG,OAAO,CAAC;AACM;AACqB;AACK;AACpD;AACA,KAAK,CAAC,MAAM,GAAGA,kBAAS,CAAC;AACzB,KAAK,CAAC,OAAO,GAAGC,OAAQ,CAAC,OAAO,CAAC;AACjC,KAAK,CAAC,KAAK,GAAGA,OAAQ,CAAC,KAAK,CAAC;AAC7B,KAAK,CAAC,KAAK,GAAGA,OAAQ,CAAC,KAAK,CAAC;AAC7B,KAAK,CAAC,MAAM,GAAGA,OAAQ,CAAC,MAAM,CAAC;AAC/B;AACA;AACA,SAAS,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE;AAC9B,EAAE,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACd;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;AACtB;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,IAAI,CAAC,CAAC;AACV,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;AACnB,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AAC5B;AACA,QAAQ,CAAC,GAAG,GAAG,CAAC;AAChB,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACjB,KAAK,MAAM;AACX,MAAM,CAAC,GAAG,CAAC,CAAC;AACZ,KAAK;AACL;AACA,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChB,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AACtB;AACA;AACA,SAAS,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE;AACxB,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC;AACJ;AACA,EAAE,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AAClB,EAAE,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AAClB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AACb,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AACb,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;AAC/C;AACA,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACrC,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACrC,IAAI,IAAI,GAAG,KAAK,CAAC;AACjB,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;AACf,IAAI,IAAI,GAAG,KAAK,CAAC;AACjB,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;AACf,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE;AACzB,MAAM,EAAE,GAAG,CAAC,CAAC;AACb,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7C,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC;AAClB;AACA,QAAQ,EAAE,GAAG,GAAG,CAAC;AACjB,KAAK;AACL,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB;AACA,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE;AACzB,MAAM,EAAE,GAAG,CAAC,CAAC;AACb,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7C,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC;AAClB;AACA,QAAQ,EAAE,GAAG,GAAG,CAAC;AACjB,KAAK;AACL,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB;AACA;AACA,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC;AACzB,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC;AACzB,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClB,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AACtB;AACA,SAAS,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7C,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACvB,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,cAAc,GAAG;AAClD,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;AAC9C,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtC,GAAG,CAAC;AACJ,CAAC;AACD,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;AACtC;AACA,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AAChE,IAAI,KAAK,CAAC;AACV,CAAC;AACD,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;AAC9B;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,OAAO,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACpC,CAAC;AACD,KAAK,CAAC,SAAS,GAAG,SAAS;;;ACrH3B,YAAY,CAAC;AACb;AAC0B;AACM;AAChC,IAAI,MAAM,GAAGC,SAAK,CAAC,MAAM,CAAC;AAC1B,IAAI,MAAM,GAAGA,SAAK,CAAC,MAAM,CAAC;AAC1B,IAAIC,QAAM,GAAGD,SAAK,CAAC,MAAM,CAAC;AAC1B;AACA,SAAS,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE;AAC/B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9B;AACA;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/D;AACA;AACA,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvC;AACA;AACA,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACxC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3D;AACA;AACA,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9B,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9B,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9B,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9B;AACA,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AACpD;AACA;AACA,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjD,EAAE,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACjD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG,MAAM;AACT,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvC,GAAG;AACH,CAAC;AACD,QAAc,GAAG,SAAS,CAAC;AAC3B;AACA,SAAS,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK,GAAG;AAC7C,EAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACrC,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,GAAG;AACnD,EAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACrC,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/D,EAAEC,QAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;AAChC;AACA,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1C,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACvE,EAAE,CAAC,IAAI,CAAC,CAAC;AACT;AACA;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE;AACjD,IAAI,IAAI,GAAG,CAAC,CAAC;AACb,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAClD,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9B,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACrB,MAAM,IAAI,IAAI,KAAK,CAAC;AACpB,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,WAAW,IAAI,IAAI,KAAK,CAAC,CAAC;AAC1B,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACjB,GAAG;AACH,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;AACvD,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ;AACA;AACA,EAAE,IAAI,SAAS,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AACrC,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC;AACpB,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;AAC7B;AACA;AACA,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1C;AACA;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1C,EAAE,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC5C;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AAC/C,MAAM,CAAC,EAAE,CAAC;AACV,IAAI,IAAI,CAAC,IAAI,CAAC;AACd,MAAM,CAAC,EAAE,CAAC;AACV,IAAI,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtB;AACA,IAAI,IAAI,CAAC,GAAG,CAAC;AACb,MAAM,MAAM;AACZ,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnB,IAAIA,QAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC7B;AACA,MAAM,IAAI,CAAC,GAAG,CAAC;AACf,QAAQ,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9C;AACA,QAAQ,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACrD,KAAK,MAAM;AACX;AACA,MAAM,IAAI,CAAC,GAAG,CAAC;AACf,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAChD,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;AAC/C,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,CAAC,IAAI;AAC3D,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,GAAG;AACL,EAAE,cAAc,EAAE;AAClB,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;AACzB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;AACzB;AACA;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC5B,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC;AAChC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AAC9B,GAAG;AACH;AACA;AACA,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACpC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AAChD,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/D,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACzC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACzC,MAAM,SAAS;AACf,KAAK;AACL;AACA,IAAI,IAAI,IAAI,GAAG;AACf,MAAM,MAAM,CAAC,CAAC,CAAC;AACf,MAAM,IAAI;AACV,MAAM,IAAI;AACV,MAAM,MAAM,CAAC,CAAC,CAAC;AACf,KAAK,CAAC;AACN;AACA;AACA,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AAC5C,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1D,KAAK,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE;AAC5D,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1D,KAAK;AACL;AACA,IAAI,IAAI,KAAK,GAAG;AAChB,MAAM,CAAC,CAAC;AACR,MAAM,CAAC,CAAC;AACR,MAAM,CAAC,CAAC;AACR,MAAM,CAAC,CAAC;AACR,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,CAAC;AACP,KAAK,CAAC;AACN;AACA,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACvC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5B,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7B,MAAM,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7B;AACA,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACpB,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1C,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;AACzB,EAAE,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd;AACA,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;AACnB,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC;AACtB,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChC,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;AACxB,UAAU,IAAI,GAAG,KAAK,CAAC;AACvB,OAAO;AACP,MAAM,IAAI,CAAC,IAAI;AACf,QAAQ,MAAM;AACd,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,CAAC,EAAE,CAAC;AACV,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC;AACd,MAAM,CAAC,EAAE,CAAC;AACV,IAAI,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,GAAG,CAAC;AACb,MAAM,MAAM;AACZ;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,MAAM,CAAC,CAAC;AACR,MAAM,IAAI,CAAC,KAAK,CAAC;AACjB,QAAQ,SAAS;AACjB,WAAW,IAAI,CAAC,GAAG,CAAC;AACpB,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,WAAW,IAAI,CAAC,GAAG,CAAC;AACpB,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACxC;AACA,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ;AAC7B,QAAQ,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9B;AACA,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,KAAK;AACL,GAAG;AACH;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAC1B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAClB;AACA,EAAE,IAAI,cAAc;AACpB,IAAI,OAAO,GAAG,CAAC;AACf;AACA,IAAI,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;AACrB,CAAC,CAAC;AACF;AACA,SAAS,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE;AAChC,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B,CAAC;AACD,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC;AAChC;AACA,SAAS,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY;AAChD,EAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACrC,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,GAAG;AACnD,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE;AACnE,EAAE,KAAK,GAAGD,SAAK,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACpC;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;AAChC;AACA;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI;AAClE,MAAM,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE;AACpC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI;AACzB,MAAMC,QAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAChD,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI;AAC9B,MAAMA,QAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAChD;AACA,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;AACjD,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACzC;AACA,IAAI,OAAO,GAAG,CAAC;AACf,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI;AACpD,cAAc,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,EAAE;AACxC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;AACvE,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;AAC1C,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,gBAAgB,CAAC,GAAG,EAAE;AACtE,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAChC,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,CAAC,OAAO,EAAE;AACxD,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;AACtC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACzC;AACA,EAAE,IAAI,OAAO;AACb,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5D;AACA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE;AAC3D,EAAE,OAAOD,SAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;AAClD,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,KAAK,EAAE;AAC5D,EAAE,IAAI,IAAI,CAAC,WAAW;AACtB,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,GAAG,EAAE,IAAI;AACb,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC;AACJ,EAAE,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1C,EAAE,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACnD,EAAE,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACrC,EAAE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACjC;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,CAAC,CAAC,EAAE;AAC1D,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW;AACvB,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACzC,EAAE,IAAI,CAAC,OAAO;AACd,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;AAChF,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AACpE,EAAE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO;AAClD,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC;AACA,EAAE,IAAI,OAAO,GAAG,EAAE,IAAI,EAAE,CAAC;AACzB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE;AACxC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE;AACjC,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;AACtB,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtB,GAAG;AACH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,OAAO;AACnB,GAAG,CAAC;AACJ,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,GAAG,EAAE;AAChE,EAAE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG;AAC9C,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;AAChC;AACA,EAAE,IAAI,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC;AACrB,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AAC3B,EAAE,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC1C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAC9B,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjC,EAAE,OAAO;AACT,IAAI,GAAG,EAAE,GAAG;AACZ,IAAI,MAAM,EAAE,GAAG;AACf,GAAG,CAAC;AACJ,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,GAAG;AACnD,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AACF;AACA,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE;AAC5C,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;AACf,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC5B,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAChB,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;;;AC5XD,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;AACzC;AACA,EAAE,cAAc,GAAG,SAAS,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE;AACtD,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,CAAC,MAAM,GAAG,UAAS;AAC7B,MAAM,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE;AAC1D,QAAQ,WAAW,EAAE;AACrB,UAAU,KAAK,EAAE,IAAI;AACrB,UAAU,UAAU,EAAE,KAAK;AAC3B,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,YAAY,EAAE,IAAI;AAC5B,SAAS;AACT,OAAO,EAAC;AACR,KAAK;AACL,GAAG,CAAC;AACJ,CAAC,MAAM;AACP;AACA,EAAE,cAAc,GAAG,SAAS,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE;AACtD,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,CAAC,MAAM,GAAG,UAAS;AAC7B,MAAM,IAAI,QAAQ,GAAG,YAAY,GAAE;AACnC,MAAM,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,UAAS;AAC9C,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,GAAE;AACrC,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,KAAI;AACvC,KAAK;AACL,IAAG;AACH;;;AC1BA,YAAY,CAAC;AACb;AACgC;AACN;AACS;AACN;AAC7B;AACA,IAAIC,QAAM,GAAGD,SAAK,CAAC,MAAM,CAAC;AAC1B;AACA,SAAS,UAAU,CAAC,IAAI,EAAE;AAC1B,EAAEE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACjC;AACA,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;AACjC;AACA,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC5D;AACA;AACA,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC1C,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,CAAC;AACDC,gBAAQ,CAAC,UAAU,EAAED,IAAI,CAAC,CAAC;AAC3B,WAAc,GAAG,UAAU,CAAC;AAC5B;AACA,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,gBAAgB,CAAC,IAAI,EAAE;AACxE;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AAC/D,IAAI,OAAO;AACX;AACA;AACA,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;AACjB,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjD,GAAG,MAAM;AACT,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5D,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AACnB,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AACrC,GAAG,MAAM;AACT;AACA,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7C,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE;AACnE,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1B,KAAK,MAAM;AACX,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1B,MAAMD,QAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,EAAE;AACzC,MAAM,OAAO;AACb,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AAC5B,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AAC5B,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACvC,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,KAAK,EAAE,KAAK;AAChB,GAAG,CAAC;AACJ,CAAC,CAAC;AACF;AACA,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,GAAG,EAAE;AACjE;AACA;AACA;AACA,EAAE,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrD,EAAE,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;AAC5C,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC5B;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/D;AACA,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACrC,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACrC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACpB,CAAC,CAAC;AACF;AACA,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,MAAM,EAAE;AACpE;AACA,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAClE;AACA;AACA;AACA,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;AACjB,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AACzB,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;AACA;AACA,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,EAAE,CAAC;AACT;AACA,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,EAAE,CAAC;AACT;AACA,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,EAAE,CAAC;AACT;AACA,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B;AACA,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACpC,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AACvB,MAAM,EAAE,GAAG,EAAE,CAAC;AACd,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACnB,MAAM,EAAE,GAAG,CAAC,CAAC;AACb,KAAK,MAAM,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE;AAChC,MAAM,MAAM;AACZ,KAAK;AACL,IAAI,KAAK,GAAG,CAAC,CAAC;AACd;AACA,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAI,EAAE,GAAG,EAAE,CAAC;AACZ,IAAI,EAAE,GAAG,CAAC,CAAC;AACX,IAAI,EAAE,GAAG,EAAE,CAAC;AACZ,IAAI,EAAE,GAAG,CAAC,CAAC;AACX,GAAG;AACH,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACf,EAAE,EAAE,GAAG,CAAC,CAAC;AACT;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC3B,IAAI,EAAE,GAAG,EAAE,CAAC;AACZ,IAAI,EAAE,GAAG,EAAE,CAAC;AACZ,GAAG;AACH;AACA;AACA,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE;AACnB,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE;AACnB,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;AACpB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;AACpB,GAAG,CAAC;AACJ,CAAC,CAAC;AACF;AACA,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,CAAC,EAAE;AACzD,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB;AACA,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9C;AACA,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB;AACA;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC7B,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;AAC5B,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC5B,CAAC,CAAC;AACF;AACA,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE;AAC9D,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACpB,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG;AACZ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;AACvB,EAAE,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAChD,IAAI,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AACrC;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC;AAClC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,KAAK;AACpC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACnB;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC;AACF;AACA,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzD,EAAE,IAAI,KAAK,CAAC,GAAG;AACf,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAClB;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7D,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC/C,CAAC,CAAC;AACF;AACA,UAAU,CAAC,SAAS,CAAC,eAAe;AACpC,IAAI,SAAS,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE;AAC7D,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AACrC,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC9C,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1B,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;AAChC;AACA,QAAQ,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;AAC/B,UAAU,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AAC1B,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;AAC/B,UAAU,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AAC1B,UAAU,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChC,SAAS;AACT;AACA,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAClC,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AAClC,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACtC,OAAO;AACP,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;AAC7E;AACA;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC1B,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC1B,OAAO;AACP,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK,CAAC;AACN;AACA,SAAS,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE;AACnC,EAAEC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AAChC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AAClB,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AAClB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AACpB,GAAG,MAAM;AACT,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3B;AACA,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;AACnB,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;AACnB,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;AACrB,GAAG;AACH,CAAC;AACDC,gBAAQ,CAAC,KAAK,EAAED,IAAI,CAAC,SAAS,CAAC,CAAC;AAChC;AACA,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE;AACzD,EAAE,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACtC,CAAC,CAAC;AACF;AACA,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE;AACtE,EAAE,OAAO,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxC,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,GAAG;AAC/C,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;AACtB,IAAI,OAAO;AACX;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;AAC7B,EAAE,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI;AACrB,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,IAAI,IAAI,OAAO,GAAG,SAAS,CAAC,EAAE;AAC9B,MAAM,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,KAAK,CAAC;AACN,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,WAAW,GAAG;AACvB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI;AACtB,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG;AACxB,QAAQ,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;AAC3C,OAAO;AACP,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI;AAC9B,QAAQ,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI;AAC9B,QAAQ,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;AAC/C,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,GAAG;AAC3C,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW;AACvB,IAAI,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;AAC9B;AACA,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,IAAI;AAC/C,IAAI,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI;AACzC,MAAM,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI;AACzC,MAAM,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI;AACjC,MAAM,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG;AACnC,MAAM,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAClD,KAAK;AACL,GAAG,EAAE,CAAC;AACN,CAAC,CAAC;AACF;AACA,KAAK,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;AACpD,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC7B,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1B,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACb,IAAI,OAAO,GAAG,CAAC;AACf;AACA,EAAE,SAAS,SAAS,CAAC,GAAG,EAAE;AAC1B,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnB,EAAE,GAAG,CAAC,WAAW,GAAG;AACpB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI;AAC5B,MAAM,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI;AAC5B,MAAM,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI;AACpB,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG;AACtB,MAAM,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,GAAG;AAC7C,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,OAAO,qBAAqB,CAAC;AACjC,EAAE,OAAO,eAAe,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACtD,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,GAAG;AACnD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AAClB,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE;AACtC;AACA,EAAE,IAAI,IAAI,CAAC,GAAG;AACd,IAAI,OAAO,CAAC,CAAC;AACb;AACA;AACA,EAAE,IAAI,CAAC,CAAC,GAAG;AACX,IAAI,OAAO,IAAI,CAAC;AAChB;AACA;AACA,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;AACtB;AACA;AACA,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC;AACA;AACA,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC3B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACrB,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AAC/C,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvD,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClC,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG;AACrC,EAAE,IAAI,IAAI,CAAC,GAAG;AACd,IAAI,OAAO,IAAI,CAAC;AAChB;AACA;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClC,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACvB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACvB;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3B,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;AAC5B,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC7D;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvD,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClC,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,GAAG;AACvC,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC1B,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,GAAG;AACvC,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC1B,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE;AACtC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACpB,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC;AAChB,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AAC9B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC5C,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI;AAC1B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;AACvD;AACA,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACrD,EAAE,IAAI,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AAC5B,EAAE,IAAI,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC1B,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI;AACrB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACtD;AACA,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACvD,EAAE,IAAI,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AAC5B,EAAE,IAAI,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC1B,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI;AACrB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC5D;AACA,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9D,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE;AACpC,EAAE,OAAO,IAAI,KAAK,CAAC;AACnB,SAAS,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG;AAC3B,cAAc,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,WAAW,EAAE;AAChD,EAAE,IAAI,IAAI,CAAC,GAAG;AACd,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AACtD,EAAE,IAAI,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE;AACvC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;AAC/B,IAAI,IAAI,MAAM,GAAG,SAAS,CAAC,EAAE;AAC7B,MAAM,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,GAAG,CAAC,WAAW,GAAG;AACtB,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI;AACtB,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG;AACxB,QAAQ,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;AAC1C,OAAO;AACP,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI;AAC9B,QAAQ,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI;AAC9B,QAAQ,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;AAC9C,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF;AACA,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG;AACrC,EAAE,IAAI,IAAI,CAAC,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC/C;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9D,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF;AACA,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,EAAEA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC/C,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AAC9C,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AAC5B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AAC5B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACvB,GAAG,MAAM;AACT,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;AACjB,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;AACjB,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;AACjB,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C;AACA,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AACxC,CAAC;AACDC,gBAAQ,CAAC,MAAM,EAAED,IAAI,CAAC,SAAS,CAAC,CAAC;AACjC;AACA,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvD,EAAE,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG;AACtC,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC;AACA,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC9B,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7C;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG;AACtC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE;AACvC;AACA,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,OAAO,CAAC,CAAC;AACb;AACA;AACA,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC;AAChB;AACA;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AACzB,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3B,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC9B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC1B,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AACvB,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACvB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACjD;AACA,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACtB,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACxB;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACxD,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1D,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxC;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,CAAC,EAAE;AACjD;AACA,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;AACnB;AACA;AACA,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC;AAChB;AACA;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3B,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC1B,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AACvB,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACvB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACjD;AACA,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACtB,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACxB;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACxD,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1D,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE;AAC3C,EAAE,IAAI,GAAG,KAAK,CAAC;AACf,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;AACtB;AACA,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AAC7C,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AACjB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAC5B,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAClB,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACvB,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAC7B;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC;AACjC;AACA;AACA,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC1B,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC5B,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;AAC5B,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAChE;AACA,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/C,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC3B,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;AACnB,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B;AACA,IAAI,EAAE,GAAG,EAAE,CAAC;AACZ,IAAI,EAAE,GAAG,EAAE,CAAC;AACZ,IAAI,GAAG,GAAG,GAAG,CAAC;AACd,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;AACrD,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG;AACtC,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK;AACtB,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC3B,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;AAC5B,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;AAC5B;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACvB,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,GAAG;AAChD,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,EAAE,CAAC;AACT;AACA,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC7B;AACA,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC7B;AACA,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AAC3B;AACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACjE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrB;AACA,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACtC;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA;AACA,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC;AACA;AACA,IAAI,EAAE,GAAG,CAAC,CAAC;AACX;AACA,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;AACA,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,GAAG,MAAM;AACT;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC5B;AACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC5B;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACvB;AACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5D,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrB;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACvB;AACA;AACA,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACxB,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACxB;AACA;AACA,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACjC;AACA,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC7C;AACA,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACxB,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,GAAG;AAClD,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,EAAE,CAAC;AACT;AACA,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC7B;AACA,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC7B;AACA,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AAC3B;AACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACjE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrB;AACA,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5D;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA,IAAI,EAAE,GAAG,CAAC,CAAC;AACX;AACA,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;AACA,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,GAAG,MAAM;AACT;AACA;AACA;AACA;AACA,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAChC;AACA,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAChC;AACA,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpC;AACA,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAClE,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;AACA,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpC,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvC;AACA,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACtE;AACA,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AACjC,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvC,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvC,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvC,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1D,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,GAAG;AACxC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACvB;AACA;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClB,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC;AACjC;AACA,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AACxB,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AACxB;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9D;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC3B,EAAE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7C,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC1B;AACA,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;AAC1B,EAAE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,EAAE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,EAAE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACtC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACpC;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,GAAG;AACxC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;AACvB,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3B;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3B;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3B;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AACzB;AACA,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACpC;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACtB;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/D,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACnB,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACpB;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AACtB;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACnB,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACnB,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACnE;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1B,EAAE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,EAAE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3C,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACtB,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACtB;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACvE,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACtB,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACtB,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACtB;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC7D;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE;AAC9C,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACvB;AACA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE;AACrC,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ;AACzB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B;AACA,EAAE,IAAI,IAAI,KAAK,CAAC;AAChB,IAAI,OAAO,IAAI,CAAC;AAChB;AACA;AACA,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3B,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AACzB,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AAC9D,IAAI,OAAO,KAAK,CAAC;AACjB;AACA;AACA,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,CAAC,EAAE;AAC7C,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC9C,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC;AAC1B,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;AACrB,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACrC,EAAE,SAAS;AACX,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAI,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjC,MAAM,OAAO,KAAK,CAAC;AACnB;AACA,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC;AAC5B,MAAM,OAAO,IAAI,CAAC;AAClB,GAAG;AACH,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,GAAG;AAC9C,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI,OAAO,sBAAsB,CAAC;AAClC,EAAE,OAAO,gBAAgB,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;AAClD,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AAC5C,CAAC,CAAC;AACF;AACA,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,GAAG;AACpD;AACA,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;;;ACz6BD,YAAY,CAAC;AACb;AACA,IAAI,KAAK,GAAG,OAAO,CAAC;AACpB;AACA,KAAK,CAAC,IAAI,GAAGE,IAAiB,CAAC;AAC/B,KAAK,CAAC,KAAK,GAAGC,OAAkB,CAAC;AACjC,KAAK,CAAC,IAAI,0CAAoB,CAAC;AAC/B,KAAK,CAAC,OAAO,6CAAuB;;;;ACPpC,YAAY,CAAC;AACb;AACA,IAAI,MAAM,GAAG,OAAO,CAAC;AACrB;AAC8B;AACC;AACA;AAC/B;AACA,IAAI,MAAM,GAAGL,SAAK,CAAC,MAAM,CAAC;AAC1B;AACA,SAAS,WAAW,CAAC,OAAO,EAAE;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO;AAC9B,IAAI,IAAI,CAAC,KAAK,GAAG,IAAIM,OAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC1C,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS;AACrC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAIA,OAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC5C;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAIA,OAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC3B;AACA,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,eAAe,CAAC,CAAC;AAC7C,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,yBAAyB,CAAC,CAAC;AACrE,CAAC;AACD,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;AACjC;AACA,SAAS,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE;AACpC,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;AACtC,IAAI,YAAY,EAAE,IAAI;AACtB,IAAI,UAAU,EAAE,IAAI;AACpB,IAAI,GAAG,EAAE,WAAW;AACpB,MAAM,IAAI,KAAK,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AAC3C,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;AAC1C,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,KAAK,EAAE,KAAK;AACpB,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA,WAAW,CAAC,MAAM,EAAE;AACpB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,CAAC,EAAE,uDAAuD;AAC5D,EAAE,CAAC,EAAE,uDAAuD;AAC5D,EAAE,CAAC,EAAE,uDAAuD;AAC5D,EAAE,CAAC,EAAE,uDAAuD;AAC5D,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;AACnB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,CAAC,EAAE;AACL,IAAI,uDAAuD;AAC3D,IAAI,uDAAuD;AAC3D,GAAG;AACH,CAAC,CAAC,CAAC;AACH;AACA,WAAW,CAAC,MAAM,EAAE;AACpB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,CAAC,EAAE,gEAAgE;AACrE,EAAE,CAAC,EAAE,gEAAgE;AACrE,EAAE,CAAC,EAAE,gEAAgE;AACrE,EAAE,CAAC,EAAE,gEAAgE;AACrE,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;AACnB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,CAAC,EAAE;AACL,IAAI,gEAAgE;AACpE,IAAI,gEAAgE;AACpE,GAAG;AACH,CAAC,CAAC,CAAC;AACH;AACA,WAAW,CAAC,MAAM,EAAE;AACpB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,KAAK,EAAE,IAAI;AACb,EAAE,CAAC,EAAE,yEAAyE;AAC9E,EAAE,CAAC,EAAE,yEAAyE;AAC9E,EAAE,CAAC,EAAE,yEAAyE;AAC9E,EAAE,CAAC,EAAE,yEAAyE;AAC9E,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;AACnB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,CAAC,EAAE;AACL,IAAI,yEAAyE;AAC7E,IAAI,yEAAyE;AAC7E,GAAG;AACH,CAAC,CAAC,CAAC;AACH;AACA,WAAW,CAAC,MAAM,EAAE;AACpB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,KAAK,EAAE,IAAI;AACb,EAAE,CAAC,EAAE,iEAAiE;AACtE,KAAK,8CAA8C;AACnD,EAAE,CAAC,EAAE,iEAAiE;AACtE,KAAK,8CAA8C;AACnD,EAAE,CAAC,EAAE,iEAAiE;AACtE,KAAK,8CAA8C;AACnD,EAAE,CAAC,EAAE,iEAAiE;AACtE,KAAK,8CAA8C;AACnD,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;AACnB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,CAAC,EAAE;AACL,IAAI,0EAA0E;AAC9E,IAAI,qCAAqC;AACzC,IAAI,0EAA0E;AAC9E,IAAI,qCAAqC;AACzC,GAAG;AACH,CAAC,CAAC,CAAC;AACH;AACA,WAAW,CAAC,MAAM,EAAE;AACpB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,KAAK,EAAE,IAAI;AACb,EAAE,CAAC,EAAE,wDAAwD;AAC7D,KAAK,wDAAwD;AAC7D,KAAK,8CAA8C;AACnD,EAAE,CAAC,EAAE,wDAAwD;AAC7D,KAAK,wDAAwD;AAC7D,KAAK,8CAA8C;AACnD,EAAE,CAAC,EAAE,wDAAwD;AAC7D,KAAK,wDAAwD;AAC7D,KAAK,8CAA8C;AACnD,EAAE,CAAC,EAAE,wDAAwD;AAC7D,KAAK,wDAAwD;AAC7D,KAAK,8CAA8C;AACnD,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;AACnB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,CAAC,EAAE;AACL,IAAI,wDAAwD;AAC5D,IAAI,wDAAwD;AAC5D,IAAI,8CAA8C;AAClD,IAAI,wDAAwD;AAC5D,IAAI,wDAAwD;AAC5D,IAAI,8CAA8C;AAClD,GAAG;AACH,CAAC,CAAC,CAAC;AACH;AACA,WAAW,CAAC,YAAY,EAAE;AAC1B,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,CAAC,EAAE,qEAAqE;AAC1E,EAAE,CAAC,EAAE,OAAO;AACZ,EAAE,CAAC,EAAE,GAAG;AACR,EAAE,CAAC,EAAE,qEAAqE;AAC1E,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;AACnB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,CAAC,EAAE;AACL,IAAI,GAAG;AACP,GAAG;AACH,CAAC,CAAC,CAAC;AACH;AACA,WAAW,CAAC,SAAS,EAAE;AACvB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,CAAC,EAAE,qEAAqE;AAC1E,EAAE,CAAC,EAAE,IAAI;AACT,EAAE,CAAC,EAAE,GAAG;AACR;AACA,EAAE,CAAC,EAAE,qEAAqE;AAC1E,EAAE,CAAC,EAAE,qEAAqE;AAC1E,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;AACnB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,CAAC,EAAE;AACL,IAAI,kEAAkE;AACtE;AACA;AACA,IAAI,kEAAkE;AACtE,GAAG;AACH,CAAC,CAAC,CAAC;AACH;AACA,IAAI,GAAG,CAAC;AACR,IAAI;AACJ,EAAE,GAAG,mEAAqC,CAAC;AAC3C,CAAC,CAAC,OAAO,CAAC,EAAE;AACZ,EAAE,GAAG,GAAG,SAAS,CAAC;AAClB,CAAC;AACD;AACA,WAAW,CAAC,WAAW,EAAE;AACzB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,CAAC,EAAE,yEAAyE;AAC9E,EAAE,CAAC,EAAE,GAAG;AACR,EAAE,CAAC,EAAE,GAAG;AACR,EAAE,CAAC,EAAE,yEAAyE;AAC9E,EAAE,CAAC,EAAE,GAAG;AACR,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;AACnB;AACA;AACA,EAAE,IAAI,EAAE,kEAAkE;AAC1E,EAAE,MAAM,EAAE,kEAAkE;AAC5E,EAAE,KAAK,EAAE;AACT,IAAI;AACJ,MAAM,CAAC,EAAE,kCAAkC;AAC3C,MAAM,CAAC,EAAE,mCAAmC;AAC5C,KAAK;AACL,IAAI;AACJ,MAAM,CAAC,EAAE,mCAAmC;AAC5C,MAAM,CAAC,EAAE,kCAAkC;AAC3C,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,CAAC,EAAE;AACL,IAAI,kEAAkE;AACtE,IAAI,kEAAkE;AACtE,IAAI,GAAG;AACP,GAAG;AACH,CAAC,CAAC;;;AC7MF,YAAY,CAAC;AACb;AAC8B;AACmB;AACL;AAC5C;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE;AAC3B,EAAE,IAAI,EAAE,IAAI,YAAY,QAAQ,CAAC;AACjC,IAAI,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;AACjC,EAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC3B,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;AACzC;AACA,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAClC,EAAE,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;AACjE;AACA,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACtB,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC7B,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AAChB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AAChB;AACA,EAAE,IAAI,OAAO,GAAGN,OAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,CAAC;AAC5E,EAAE,IAAI,KAAK,GAAGA,OAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,CAAC;AACtE,EAAE,IAAI,IAAI,GAAGA,OAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC;AACnE,EAAEC,kBAAM,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAChD,SAAS,kCAAkC,GAAG,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC;AACzE,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AACD,YAAc,GAAG,QAAQ,CAAC;AAC1B;AACA,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;AAC/D,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChD;AACA,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACtC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACtC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACrB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACrB,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACnB,EAAE,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;AACxC,CAAC,CAAC;AACF;AACA,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,IAAI,GAAG;AAC3C,EAAE,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC;AACF;AACA,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,MAAM,CAAC,IAAI,EAAE;AACnD,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE;AACzB,kBAAkB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAChC,kBAAkB,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AACnC,EAAE,IAAI,IAAI;AACV,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACzB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAChD,EAAE,IAAI,CAAC,IAAI;AACX,IAAI,OAAO;AACX;AACA,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;AACvB,gBAAgB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9B,gBAAgB,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC;AAChC,gBAAgB,MAAM,CAAC,IAAI,CAAC;AAC5B,gBAAgB,MAAM,EAAE,CAAC;AACzB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAChD,CAAC,CAAC;AACF;AACA,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,MAAM,EAAE;AAC9E;AACA,EAAE,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACtC,IAAI,MAAM,GAAG,GAAG,CAAC;AACjB,IAAI,GAAG,GAAG,UAAU,CAAC;AACrB,IAAI,UAAU,GAAG,IAAI,CAAC;AACtB,GAAG;AACH;AACA,EAAE,OAAO,GAAGD,OAAK,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAC/C,EAAE,GAAG,GAAGA,OAAK,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACnC;AACA,EAAEC,kBAAM,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAChD,SAAS,kCAAkC,GAAG,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC;AACzE;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1C,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACnB,CAAC,CAAC;AACF;AACA,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE;AACvE,EAAE,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc;AACxC,IAAI,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC1C;AACA;AACA,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,MAAM,GAAG,GAAG,CAAC;AACjB,IAAI,GAAG,GAAG,GAAG,CAAC;AACd,IAAI,GAAG,GAAG,IAAI,CAAC;AACf,GAAG;AACH;AACA;AACA,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,GAAG,GAAGD,OAAK,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACtB,GAAG;AACH;AACA,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;AAC5B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAClD,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACpB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACjB,EAAE,OAAOA,OAAK,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAChC,CAAC;;AChHD,YAAY,CAAC;AACb;AAC0B;AACM;AAChC,IAAIC,QAAM,GAAGD,SAAK,CAAC,MAAM,CAAC;AAC1B;AACA,SAAS,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE;AAC9B,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACf,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AAClB;AACA;AACA,EAAE,IAAI,OAAO,CAAC,IAAI;AAClB,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACvD,EAAE,IAAI,OAAO,CAAC,GAAG;AACjB,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACpD,CAAC;AACD,OAAc,GAAG,OAAO,CAAC;AACzB;AACA,OAAO,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACvD,EAAE,IAAI,GAAG,YAAY,OAAO;AAC5B,IAAI,OAAO,GAAG,CAAC;AACf;AACA,EAAE,OAAO,IAAI,OAAO,CAAC,EAAE,EAAE;AACzB,IAAI,GAAG,EAAE,GAAG;AACZ,IAAI,MAAM,EAAE,GAAG;AACf,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF;AACA,OAAO,CAAC,WAAW,GAAG,SAAS,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC1D,EAAE,IAAI,IAAI,YAAY,OAAO;AAC7B,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,EAAE,OAAO,IAAI,OAAO,CAAC,EAAE,EAAE;AACzB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF;AACA,OAAO,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,GAAG;AACjD,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;AAC7B;AACA,EAAE,IAAI,GAAG,CAAC,UAAU,EAAE;AACtB,IAAI,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC;AAC3D,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACrB,IAAI,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,2BAA2B,EAAE,CAAC;AAClE,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;AAC5C,IAAI,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,qBAAqB,EAAE,CAAC;AAC5D;AACA,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACxC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,CAAC,OAAO,EAAE,GAAG,EAAE;AAC/D;AACA,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACnC,IAAI,GAAG,GAAG,OAAO,CAAC;AAClB,IAAI,OAAO,GAAG,IAAI,CAAC;AACnB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG;AACf,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC;AACpB;AACA,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACvC,CAAC,CAAC;AACF;AACA,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,GAAG,EAAE;AACxD,EAAE,IAAI,GAAG,KAAK,KAAK;AACnB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACrC;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,CAAC,CAAC;AACF;AACA,OAAO,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;AACrE,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;AACrC;AACA;AACA;AACA,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC;AACF;AACA,OAAO,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE;AACnE,EAAE,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE;AACtB;AACA;AACA;AACA,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;AACvC,MAAMC,QAAM,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;AACzC,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO;AAC7C,eAAe,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;AACjD,MAAMA,QAAM,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,8BAA8B,CAAC,CAAC;AAC7D,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,IAAI,OAAO;AACX,GAAG;AACH,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACjD,CAAC,CAAC;AACF;AACA;AACA,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,GAAG,EAAE;AAChD,EAAE,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;AACtB,IAAIA,QAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,4BAA4B,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;AACnC,CAAC,CAAC;AACF;AACA;AACA,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE;AAC1D,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC,CAAC;AACF;AACA,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE;AAC3D,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAC9C,CAAC,CAAC;AACF;AACA,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,GAAG;AAC/C,EAAE,OAAO,aAAa,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACjE,SAAS,QAAQ,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;AAC5D,CAAC;;ACxHD,YAAY,CAAC;AACb;AAC0B;AAC1B;AACgC;AAChC,IAAIA,QAAM,GAAGD,SAAK,CAAC,MAAM,CAAC;AAC1B;AACA,SAAS,SAAS,CAAC,OAAO,EAAE,GAAG,EAAE;AACjC,EAAE,IAAI,OAAO,YAAY,SAAS;AAClC,IAAI,OAAO,OAAO,CAAC;AACnB;AACA,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC;AACnC,IAAI,OAAO;AACX;AACA,EAAEC,QAAM,CAAC,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAAC;AAC7D,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS;AACzC,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AAC/C,CAAC;AACD,aAAc,GAAG,SAAS,CAAC;AAC3B;AACA,SAAS,QAAQ,GAAG;AACpB,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACjB,CAAC;AACD;AACA,SAAS,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE;AAC3B,EAAE,IAAI,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;AAC/B,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC,EAAE;AACzB,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG,OAAO,GAAG,GAAG,CAAC;AAC/B;AACA;AACA,EAAE,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;AACtC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACd,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;AAC3D,IAAI,GAAG,KAAK,CAAC,CAAC;AACd,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,IAAI,GAAG,MAAM,CAAC,CAAC;AACf,GAAG;AACH;AACA;AACA,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE;AACnB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAChB,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA,SAAS,SAAS,CAAC,GAAG,EAAE;AACxB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3B,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;AACrD,IAAI,CAAC,EAAE,CAAC;AACR,GAAG;AACH,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACf,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;AACD;AACA,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;AAChE,EAAE,IAAI,GAAGD,SAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAClC,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ,EAAE,CAAC;AACzB,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE;AAChC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC/B,EAAE,IAAI,GAAG,KAAK,KAAK,EAAE;AACrB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,MAAM,EAAE;AACvC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE;AAChC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAChC,EAAE,IAAI,IAAI,KAAK,KAAK,EAAE;AACtB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9C,EAAE,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC;AAClB,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE;AAChC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAChC,EAAE,IAAI,IAAI,KAAK,KAAK,EAAE;AACtB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE;AACtC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AAClB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;AACrB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrB,KAAK,MAAM;AACX;AACA,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AAClB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;AACrB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrB,KAAK,MAAM;AACX;AACA,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC5B;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AACF;AACA,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE;AACnC,EAAE,IAAI,GAAG,GAAG,IAAI,EAAE;AAClB,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB,IAAI,OAAO;AACX,GAAG;AACH,EAAE,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AACpD,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;AAC1B,EAAE,OAAO,EAAE,MAAM,EAAE;AACnB,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;AAC7C,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,CAAC;AACD;AACA,SAAS,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK,CAAC,GAAG,EAAE;AAChD,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC3B,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC3B;AACA;AACA,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACjB,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB;AACA,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACjB,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB;AACA,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACnB,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE;AAClC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC;AACrB,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACjC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjB,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACjC,EAAE,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,EAAE,IAAI,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC;AACrB,EAAE,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACxC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,EAAE,OAAOA,SAAK,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAChC,CAAC;;ACrKD,YAAY,CAAC;AACb;AAC0B;AACU;AACJ;AACE;AAClC,IAAI,IAAI,qFAAqB,CAAC;AAC9B,IAAIC,QAAM,GAAGD,SAAK,CAAC,MAAM,CAAC;AAC1B;AAC+B;AACQ;AACvC;AACA,SAAS,EAAE,CAAC,OAAO,EAAE;AACrB,EAAE,IAAI,EAAE,IAAI,YAAY,EAAE,CAAC;AAC3B,IAAI,OAAO,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC;AAC3B;AACA;AACA,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACnC,IAAIC,QAAM,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAACM,QAAM,EAAE,OAAO,CAAC;AAChE,MAAM,gBAAgB,GAAG,OAAO,CAAC,CAAC;AAClC;AACA,IAAI,OAAO,GAAGA,QAAM,CAAC,OAAO,CAAC,CAAC;AAC9B,GAAG;AACH;AACA;AACA,EAAE,IAAI,OAAO,YAAYA,QAAM,CAAC,WAAW;AAC3C,IAAI,OAAO,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;AACjC;AACA,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;AACnC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB;AACA;AACA,EAAE,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3B,EAAE,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;AACrD;AACA;AACA,EAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;AACjD,CAAC;AACD,MAAc,GAAG,EAAE,CAAC;AACpB;AACA,EAAE,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO,CAAC,OAAO,EAAE;AACjD,EAAE,OAAO,IAAIC,GAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACpC,CAAC,CAAC;AACF;AACA,EAAE,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE;AACjE,EAAE,OAAOA,GAAO,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAC9C,CAAC,CAAC;AACF;AACA,EAAE,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE;AAC9D,EAAE,OAAOA,GAAO,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC5C,CAAC,CAAC;AACF;AACA,EAAE,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,CAAC,OAAO,EAAE;AACvD,EAAE,IAAI,CAAC,OAAO;AACd,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB;AACA;AACA,EAAE,IAAI,IAAI,GAAG,IAAIC,QAAQ,CAAC;AAC1B,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI;AACnB,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI;AACtB,IAAI,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,MAAM;AACtC,IAAI,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;AAC5D,IAAI,UAAU,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,IAAI,MAAM;AAC/D,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;AAC3B,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;AAClC,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,EAAE,SAAS;AACX,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5C,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACzB,MAAM,SAAS;AACf;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AACrC,GAAG;AACH,CAAC,CAAC;AACF;AACA,EAAE,CAAC,SAAS,CAAC,YAAY,GAAG,SAAS,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE;AAClE,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;AACxD,EAAE,IAAI,KAAK,GAAG,CAAC;AACf,IAAI,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC3B,EAAE,IAAI,CAAC,SAAS,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AACxC,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3B;AACA,IAAI,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AACF;AACA,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE;AAC1D,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC/B,IAAI,OAAO,GAAG,GAAG,CAAC;AAClB,IAAI,GAAG,GAAG,IAAI,CAAC;AACf,GAAG;AACH,EAAE,IAAI,CAAC,OAAO;AACd,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB;AACA,EAAE,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACtC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3C;AACA;AACA,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;AAClC,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACnD;AACA;AACA,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACvC;AACA;AACA,EAAE,IAAI,IAAI,GAAG,IAAIA,QAAQ,CAAC;AAC1B,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI;AACnB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI;AACtB,IAAI,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,MAAM;AACtC,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC;AACA,EAAE,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE;AAC/B,IAAI,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;AACrB,MAAM,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;AACrB,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACjD,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;AACzC,MAAM,SAAS;AACf;AACA,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAI,IAAI,EAAE,CAAC,UAAU,EAAE;AACvB,MAAM,SAAS;AACf;AACA,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACvB,MAAM,SAAS;AACf;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAClE,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACvB,MAAM,SAAS;AACf;AACA,IAAI,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC;AAClD,yBAAyB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACnD;AACA;AACA,IAAI,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;AACjD,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxB,MAAM,aAAa,IAAI,CAAC,CAAC;AACzB,KAAK;AACL;AACA,IAAI,OAAO,IAAIC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,aAAa,EAAE,CAAC,CAAC;AACvE,GAAG;AACH,CAAC,CAAC;AACF;AACA,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,CAAC,GAAG,EAAEC,WAAS,EAAE,GAAG,EAAE,GAAG,EAAE;AAChE,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3C,EAAE,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACrC,EAAEA,WAAS,GAAG,IAAID,SAAS,CAACC,WAAS,EAAE,KAAK,CAAC,CAAC;AAC9C;AACA;AACA,EAAE,IAAI,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AACzC,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AACzC,IAAI,OAAO,KAAK,CAAC;AACjB;AACA;AACA,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpC,EAAE,IAAI,CAAC,CAAC;AACR;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;AACjC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,CAAC,UAAU,EAAE;AACtB,MAAM,OAAO,KAAK,CAAC;AACnB;AACA,IAAI,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE;AACpB,IAAI,OAAO,KAAK,CAAC;AACjB;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CAAC;AACF;AACA,EAAE,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,GAAG,EAAEA,WAAS,EAAE,CAAC,EAAE,GAAG,EAAE;AAC9D,EAAEV,QAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,0CAA0C,CAAC,CAAC;AACpE,EAAEU,WAAS,GAAG,IAAID,SAAS,CAACC,WAAS,EAAE,GAAG,CAAC,CAAC;AAC5C;AACA,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACjB,EAAE,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,GAAGA,WAAS,CAAC,CAAC,CAAC;AACtB;AACA;AACA,EAAE,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,EAAE,IAAI,WAAW,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,WAAW;AAChE,IAAI,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;AAC5D;AACA;AACA,EAAE,IAAI,WAAW;AACjB,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3D;AACA,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACzC;AACA,EAAE,IAAI,IAAI,GAAGA,WAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AAClC,CAAC,CAAC;AACF;AACA,EAAE,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAS,CAAC,EAAEA,WAAS,EAAE,CAAC,EAAE,GAAG,EAAE;AAClE,EAAEA,WAAS,GAAG,IAAID,SAAS,CAACC,WAAS,EAAE,GAAG,CAAC,CAAC;AAC5C,EAAE,IAAIA,WAAS,CAAC,aAAa,KAAK,IAAI;AACtC,IAAI,OAAOA,WAAS,CAAC,aAAa,CAAC;AACnC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9B,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAAEA,WAAS,EAAE,CAAC,CAAC,CAAC;AACnD,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,SAAS;AACf,KAAK;AACL;AACA,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,MAAM,OAAO,CAAC,CAAC;AACf,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;AAC1D,CAAC;;;AClPD,YAAY,CAAC;AACb;AACA,IAAI,QAAQ,GAAG,OAAO,CAAC;AACvB;AACA,QAAQ,CAAC,OAAO,wCAA6B,CAAC,OAAO,CAAC;AACtD,QAAQ,CAAC,KAAK,GAAGP,SAA2B,CAAC;AAC7C,QAAQ,CAAC,IAAI,qFAAqB,CAAC;AACnC,QAAQ,CAAC,KAAK,GAAGC,OAA2B,CAAC;AAC7C,QAAQ,CAAC,MAAM,GAAGO,QAA4B,CAAC;AAC/C;AACA;AACA,QAAQ,CAAC,EAAE,GAAGC,EAAwB,CAAC;AACvC,QAAQ,CAAC,KAAK,oDAA8B;;;ACXzC,IAACC,IAAE,GAAGC,UAAG,CAAC;;;;"}