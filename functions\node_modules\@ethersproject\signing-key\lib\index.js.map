{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;AAEb,uCAAgC;AAEhC,8CAAyI;AACzI,wDAA2D;AAE3D,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,IAAI,MAAM,GAAO,IAAI,CAAA;AACrB,SAAS,QAAQ;IACb,IAAI,CAAC,MAAM,EAAE;QACT,MAAM,GAAG,IAAI,aAAE,CAAC,WAAW,CAAC,CAAC;KAChC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;IAYI,oBAAY,UAAqB;QAC7B,IAAA,2BAAc,EAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAE3C,IAAA,2BAAc,EAAC,IAAI,EAAE,YAAY,EAAE,IAAA,eAAO,EAAC,UAAU,CAAC,CAAC,CAAC;QACxD,IAAI,IAAA,qBAAa,EAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE;YACvC,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;SACpF;QAED,IAAM,OAAO,GAAG,QAAQ,EAAE,CAAC,cAAc,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAErE,IAAA,2BAAc,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QAC1E,IAAA,2BAAc,EAAC,IAAI,EAAE,qBAAqB,EAAE,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAEnF,IAAA,2BAAc,EAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,8BAAS,GAAT,UAAU,KAAgB;QACtB,IAAM,EAAE,GAAI,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/D,IAAM,EAAE,GAAI,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,+BAAU,GAAV,UAAW,MAAiB;QACxB,IAAM,OAAO,GAAG,QAAQ,EAAE,CAAC,cAAc,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACrE,IAAM,WAAW,GAAG,IAAA,gBAAQ,EAAC,MAAM,CAAC,CAAC;QACrC,IAAI,WAAW,CAAC,MAAM,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;SACpE;QACD,IAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACjE,OAAO,IAAA,sBAAc,EAAC;YAClB,aAAa,EAAE,SAAS,CAAC,aAAa;YACtC,CAAC,EAAE,IAAA,kBAAU,EAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,CAAC,EAAE,IAAA,kBAAU,EAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;SACrD,CAAC,CAAA;IACN,CAAC;IAED,wCAAmB,GAAnB,UAAoB,QAAmB;QACnC,IAAM,OAAO,GAAG,QAAQ,EAAE,CAAC,cAAc,CAAC,IAAA,gBAAQ,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACrE,IAAM,YAAY,GAAG,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAA,gBAAQ,EAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpF,OAAO,IAAA,kBAAU,EAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACxF,CAAC;IAEM,uBAAY,GAAnB,UAAoB,KAAU;QAC1B,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;IAC5C,CAAC;IACL,iBAAC;AAAD,CAAC,AAzDD,IAyDC;AAzDY,gCAAU;AA2DvB,SAAgB,gBAAgB,CAAC,MAAiB,EAAE,SAAwB;IACxE,IAAM,GAAG,GAAG,IAAA,sBAAc,EAAC,SAAS,CAAC,CAAC;IACtC,IAAM,EAAE,GAAG,EAAE,CAAC,EAAE,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IACtD,OAAO,IAAI,GAAG,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAA,gBAAQ,EAAC,MAAM,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACzG,CAAC;AAJD,4CAIC;AAED,SAAgB,gBAAgB,CAAC,GAAc,EAAE,UAAoB;IACjE,IAAM,KAAK,GAAG,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAC;IAE5B,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;QACrB,IAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,UAAU,EAAE;YACZ,OAAO,IAAI,GAAG,QAAQ,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACzE;QACD,OAAO,UAAU,CAAC,SAAS,CAAC;KAE/B;SAAM,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,IAAI,UAAU,EAAE;YAAE,OAAO,IAAA,eAAO,EAAC,KAAK,CAAC,CAAC;SAAE;QAC1C,OAAO,IAAI,GAAG,QAAQ,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KAEzE;SAAM,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,IAAA,eAAO,EAAC,KAAK,CAAC,CAAC;SAAE;QAC3C,OAAO,IAAI,GAAG,QAAQ,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACxE;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,+BAA+B,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;AAC3F,CAAC;AApBD,4CAoBC"}