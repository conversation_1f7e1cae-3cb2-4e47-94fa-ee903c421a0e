{"version": 3, "file": "infura-provider.js", "sourceRoot": "", "sources": ["../src.ts/infura-provider.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;AAGb,wDAA2D;AAG3D,2DAAyD;AACzD,yCAAwE;AAExE,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,iEAA6D;AAG7D,IAAM,gBAAgB,GAAG,kCAAkC,CAAA;AAE3D;IAA6C,2CAAiB;IAK1D,iCAAY,OAAoB,EAAE,MAAY;QAA9C,iBAeC;QAdG,IAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACrD,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACvC,IAAI,UAAU,CAAC,QAAQ,EAAE;YACrB,MAAM,CAAC,UAAU,CAAC,8CAA8C,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBACnG,SAAS,EAAE,uCAAuC;aACrD,CAAC,CAAC;SACN;QAED,IAAM,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC9E,QAAA,kBAAM,GAAG,EAAE,OAAO,CAAC,SAAC;QAEpB,IAAA,2BAAc,EAAC,KAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QACnD,IAAA,2BAAc,EAAC,KAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QACtD,IAAA,2BAAc,EAAC,KAAI,EAAE,eAAe,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;;IAClE,CAAC;IAED,qDAAmB,GAAnB;QACI,OAAO,CAAC,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,CAAC;IACjD,CAAC;IACL,8BAAC;AAAD,CAAC,AAzBD,CAA6C,sCAAiB,GAyB7D;AAzBY,0DAAuB;AA2BpC;IAAoC,kCAAkB;IAAtD;;IAiGA,CAAC;IA7FU,mCAAoB,GAA3B,UAA4B,OAAoB,EAAE,MAAY;QAC1D,OAAO,IAAI,uBAAuB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACxD,CAAC;IAEM,wBAAS,GAAhB,UAAiB,MAAW;QACxB,IAAM,SAAS,GAAiE;YAC5E,MAAM,EAAE,gBAAgB;YACxB,SAAS,EAAE,gBAAgB;YAC3B,aAAa,EAAE,IAAI;SACtB,CAAC;QAEF,IAAI,MAAM,IAAI,IAAI,EAAE;YAAE,OAAO,SAAS,CAAC;SAAE;QAEzC,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;YAC7B,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC;SAEhC;aAAM,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;YACrC,MAAM,CAAC,cAAc,CAAC,CAAC,OAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,QAAQ,CAAC,EACzD,oCAAoC,EAAE,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YACzE,MAAM,CAAC,cAAc,CAAC,CAAC,OAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,QAAQ,CAAC,EAC7D,uBAAuB,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;YAE5D,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;YACvC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;SAElD;aAAM,IAAI,MAAM,CAAC,SAAS,EAAE;YACzB,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;SAC1C;QAED,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC;QAEvC,OAAO,SAAS,CAAC;IACrB,CAAC;IAEM,qBAAM,GAAb,UAAc,OAAgB,EAAE,MAAW;QACvC,IAAI,IAAI,GAAW,IAAI,CAAC;QACxB,QAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAA,CAAC,CAAC,SAAS,EAAE;YACtC,KAAK,WAAW;gBACZ,IAAI,GAAG,mBAAmB,CAAC;gBAC3B,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,GAAG,kBAAkB,CAAC;gBAC1B,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,GAAG,mBAAmB,CAAC;gBAC3B,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,GAAG,2BAA2B,CAAC;gBACnC,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,0BAA0B,CAAC;gBAClC,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,4BAA4B,CAAC;gBACpC,MAAM;YACV,KAAK,iBAAiB;gBAClB,IAAI,GAAG,2BAA2B,CAAC;gBACnC,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,4BAA4B,CAAC;gBACpC,MAAM;YACV,KAAK,iBAAiB;gBAClB,IAAI,GAAG,2BAA2B,CAAC;gBACnC,MAAM;YACV;gBACI,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,eAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;oBACrE,QAAQ,EAAE,SAAS;oBACnB,KAAK,EAAE,OAAO;iBACjB,CAAC,CAAC;SACV;QAED,IAAM,UAAU,GAAmB;YAC/B,SAAS,EAAE,IAAI;YACf,GAAG,EAAE,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;YACzD,gBAAgB,EAAE,UAAC,OAAe,EAAE,GAAW;gBAC3C,IAAI,MAAM,CAAC,SAAS,KAAK,gBAAgB,EAAE;oBACvC,IAAA,+BAAmB,GAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC;SACJ,CAAC;QAEF,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;YAC9B,UAAU,CAAC,IAAI,GAAG,EAAE,CAAC;YACrB,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAA;SAC7C;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,4CAAmB,GAAnB;QACI,OAAO,CAAC,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,CAAC;IACjD,CAAC;IACL,qBAAC;AAAD,CAAC,AAjGD,CAAoC,0CAAkB,GAiGrD;AAjGY,wCAAc"}