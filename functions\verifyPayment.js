const functions = require('firebase-functions');
const admin = require('firebase-admin');
const { ethers } = require('ethers');

// Ronin Mainnet RPC URL
const roninProviderUrl = 'https://api.roninchain.com/rpc';
const web3 = new ethers.providers.JsonRpcProvider(roninProviderUrl);

exports.verifyPayment = functions.https.onCall(async (data, context) => {
    // Check if the user is authenticated
    if (!context.auth) {
        return { status: 'failed', message: 'Authentication required.' };
    }

    const userId = context.auth.uid;
    const transactionHash = data.transactionHash;
    const tournamentId = data.tournamentId;

    if (!transactionHash || !tournamentId) {
        return { status: 'failed', message: 'Missing transactionHash or tournamentId.' };
    }

    try {
        // 1. Fetch tournament details from Firestore
        const tournamentRef = admin.firestore().collection('tournaments').doc(tournamentId);
        const tournamentDoc = await tournamentRef.get();

        if (!tournamentDoc.exists) {
            return { status: 'failed', message: 'Tournament not found.' };
        }

        const tournamentData = tournamentDoc.data();
        const requiredConfirmations = 3;

        // 2. Get transaction details from the blockchain
        const transaction = await web3.eth.getTransaction(transactionHash);

        if (!transaction) {
            return { status: 'failed', message: 'Transaction not found on the blockchain.' };
        }

        // 3. Verify transaction details
        const entryFee = tournamentData.entryFee;
        const [feeAmountStr, feeTokenSymbol] = entryFee.split(' ');
        const feeAmount = parseFloat(feeAmountStr);

        if (feeTokenSymbol === 'RON') {
            const expectedWei = web3.utils.toWei(feeAmount.toString(), 'ether');
            if (BigInt(transaction.value) < BigInt(expectedWei)) {
                return { status: 'failed', message: `Transaction value (${transaction.value}) is less than required entry fee (${expectedWei} wei).` };
            }

            if (transaction.to.toLowerCase() !== tournamentData.creatorAddress.toLowerCase()) {
                return { status: 'failed', message: `Transaction recipient (${transaction.to}) does not match tournament creator address (${tournamentData.creatorAddress}).` };
            }
        } else {
            // TODO: Implement ERC20 token verification logic
            return { status: 'failed', message: `ERC20 token verification is not yet implemented.` };
        }

        // 4. Check for block confirmations
        if (!transaction.blockNumber) {
            return { status: 'failed', message: 'Transaction is pending and has not been mined yet.' };
        }

        const latestBlock = await web3.eth.getBlockNumber();
        const confirmations = latestBlock - BigInt(transaction.blockNumber) + BigInt(1);

        if (confirmations < requiredConfirmations) {
            return {
                status: 'processing',
                message: `Transaction has ${confirmations} confirmations, but ${requiredConfirmations} are required.`,
                confirmations: Number(confirmations),
                requiredConfirmations: requiredConfirmations
            };
        }

        // 5. Check if user is already a participant
        if (tournamentData.participants && tournamentData.participants.includes(userId)) {
            return { status: 'failed', message: 'User is already a participant in this tournament.' };
        }

        // 6. Check if tournament is full (only if maxEntries is set)
        if (tournamentData.maxEntries !== 0 && tournamentData.participantCount >= tournamentData.maxEntries) {
            return { status: 'failed', message: 'Tournament is full.' };
        }

        // 7. Update Firestore using a transaction to ensure atomicity
        await admin.firestore().runTransaction(async (transaction) => {
            const doc = await transaction.get(tournamentRef);
            if (!doc.exists) {
                throw new Error('Tournament no longer exists.');
            }

            const currentData = doc.data();
            if (currentData.participants.includes(userId)) {
                throw new Error('User already joined while transaction was processing.');
            }

            if (currentData.maxEntries !== 0 && currentData.participantCount >= currentData.maxEntries) {
                throw new Error('Tournament became full while transaction was processing.');
            }

            transaction.update(tournamentRef, {
                participants: admin.firestore.FieldValue.arrayUnion(userId),
                participantCount: admin.firestore.FieldValue.increment(1),
                transactionHashes: admin.firestore.FieldValue.arrayUnion({
                    user: userId,
                    hash: transactionHash,
                    timestamp: admin.firestore.FieldValue.serverTimestamp()
                })
            });
        });

        return { status: 'success', message: 'Payment verified and user added to tournament.' };

    } catch (error) {
        console.error('Error verifying payment:', error);
        return { status: 'error', message: `An error occurred: ${error.message}` };
    }
});