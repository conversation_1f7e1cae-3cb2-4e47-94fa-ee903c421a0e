Etehreum Constants
==================

This sub-module is part of the [ethers project](https://github.com/ethers-io/ethers.js).

It contains many frequently used constants when dealing with Ethereum.

For more information, see the [documentation](https://docs.ethers.io/v5/api/utils/constants/).

Importing
---------

Most users will prefer to use the [umbrella package](https://www.npmjs.com/package/ethers),
but for those with more specific needs, individual components can be imported.

```javascript
const {

    AddressZero,
    HashZero,

    EtherSymbol,

    NegativeOne,
    Zero,
    One,
    Two,

    <PERSON><PERSON><PERSON><PERSON>,
    MaxUint256

} = require("@ethersproject/constants");
```


License
-------

MIT License
