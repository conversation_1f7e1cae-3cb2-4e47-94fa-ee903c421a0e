{"version": 3, "file": "utf8.js", "sourceRoot": "", "sources": ["../src.ts/utf8.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;AAEb,8CAA2D;AAE3D,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,+BAA+B;AAE/B,IAAY,wBAMX;AAND,WAAY,wBAAwB;IAChC,wCAAa,CAAA;IACb,uCAAgB,CAAA;IAChB,uCAAgB,CAAA;IAChB,yCAAiB,CAAA;IACjB,yCAAiB,CAAA;AACrB,CAAC,EANW,wBAAwB,GAAxB,gCAAwB,KAAxB,gCAAwB,QAMnC;AAAA,CAAC;AAEF,IAAY,eA+BX;AA/BD,WAAY,eAAe;IACvB,sEAAsE;IACtE,8CAA8C;IAC9C,uEAAsD,CAAA;IAEtD,0EAA0E;IAC1E,8CAA8C;IAC9C,sDAA8C,CAAA;IAE9C,4DAA4D;IAC5D,8CAA8C;IAC9C,6CAAwC,CAAA;IAExC,yDAAyD;IACzD,6DAA6D;IAC7D,iEAAmD,CAAA;IAEnD,yDAAyD;IACzD,2CAA2C;IAC3C,mEAAmE;IACnE,sDAA4C,CAAA;IAE5C,uDAAuD;IACvD,2CAA2C;IAC3C,6EAA6E;IAC7E,uDAA0C,CAAA;IAE1C,2CAA2C;IAC3C,2CAA2C;IAC3C,kEAAkE;IAClE,uDAAiD,CAAA;AACrD,CAAC,EA/BW,eAAe,GAAf,uBAAe,KAAf,uBAAe,QA+B1B;AAAA,CAAC;AAKF,SAAS,SAAS,CAAC,MAAuB,EAAE,MAAc,EAAE,KAAwB,EAAE,MAAqB,EAAE,YAAqB;IAC9H,OAAO,MAAM,CAAC,kBAAkB,CAAC,iCAAgC,MAAM,UAAO,MAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7G,CAAC;AAED,SAAS,UAAU,CAAC,MAAuB,EAAE,MAAc,EAAE,KAAwB,EAAE,MAAqB,EAAE,YAAqB;IAE/H,uGAAuG;IACvG,IAAI,MAAM,KAAK,eAAe,CAAC,UAAU,IAAI,MAAM,KAAK,eAAe,CAAC,mBAAmB,EAAE;QACzF,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;gBAAE,MAAM;aAAE;YACtC,CAAC,EAAE,CAAC;SACP;QACD,OAAO,CAAC,CAAC;KACZ;IAED,wEAAwE;IACxE,mEAAmE;IACnE,IAAI,MAAM,KAAK,eAAe,CAAC,OAAO,EAAE;QACpC,OAAO,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;KACpC;IAED,kBAAkB;IAClB,OAAO,CAAC,CAAC;AACb,CAAC;AAED,SAAS,WAAW,CAAC,MAAuB,EAAE,MAAc,EAAE,KAAwB,EAAE,MAAqB,EAAE,YAAqB;IAEhI,sFAAsF;IACtF,IAAI,MAAM,KAAK,eAAe,CAAC,QAAQ,EAAE;QACrC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,CAAC,CAAC;KACZ;IAED,gDAAgD;IAChD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAEpB,2CAA2C;IAC3C,OAAO,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;AACnE,CAAC;AAED,kCAAkC;AACrB,QAAA,cAAc,GAAwC,MAAM,CAAC,MAAM,CAAC;IAC7E,KAAK,EAAE,SAAS;IAChB,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,WAAW;CACvB,CAAC,CAAC;AAEH,oFAAoF;AACpF,SAAS,iBAAiB,CAAC,KAAgB,EAAE,OAAuB;IAChE,IAAI,OAAO,IAAI,IAAI,EAAE;QAAE,OAAO,GAAG,sBAAc,CAAC,KAAK,CAAC;KAAE;IAExD,KAAK,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAExB,IAAM,MAAM,GAAkB,EAAE,CAAC;IACjC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,4BAA4B;IAC5B,OAAM,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE;QAEpB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAErB,YAAY;QACZ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACd,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACf,SAAS;SACZ;QAED,qDAAqD;QACrD,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,IAAI,YAAY,GAAG,IAAI,CAAC;QAExB,sBAAsB;QACtB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YACrB,WAAW,GAAG,CAAC,CAAC;YAChB,YAAY,GAAG,IAAI,CAAC;YAExB,gCAAgC;SAC/B;aAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YAC5B,WAAW,GAAG,CAAC,CAAC;YAChB,YAAY,GAAG,KAAK,CAAC;YAEzB,0CAA0C;SACzC;aAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;YAC5B,WAAW,GAAG,CAAC,CAAC;YAChB,YAAY,GAAG,MAAM,CAAC;SAEzB;aAAM;YACH,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;gBACrB,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aAC3E;iBAAM;gBACH,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aAClE;YACD,SAAS;SACZ;QAED,uCAAuC;QACvC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,IAAI,KAAK,CAAC,MAAM,EAAE;YACrC,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAC5D,SAAS;SACZ;QAED,yCAAyC;QACzC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAExB,4BAA4B;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE;gBAC3B,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBACjE,GAAG,GAAG,IAAI,CAAC;gBACX,MAAM;aACT;YAAA,CAAC;YAEF,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;YACrC,CAAC,EAAE,CAAC;SACP;QAED,+CAA+C;QAC/C,IAAI,GAAG,KAAK,IAAI,EAAE;YAAE,SAAS;SAAE;QAE/B,qBAAqB;QACrB,IAAI,GAAG,GAAG,QAAQ,EAAE;YAChB,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YACpF,SAAS;SACZ;QAED,uCAAuC;QACvC,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,EAAE;YAChC,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YACvF,SAAS;SACZ;QAED,wDAAwD;QACxD,IAAI,GAAG,IAAI,YAAY,EAAE;YACrB,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YAChF,SAAS;SACZ;QAED,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACpB;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,uFAAuF;AACvF,SAAgB,WAAW,CAAC,GAAW,EAAE,IAAiE;IAAjE,qBAAA,EAAA,OAAiC,wBAAwB,CAAC,OAAO;IAEtG,IAAI,IAAI,IAAI,wBAAwB,CAAC,OAAO,EAAE;QAC1C,MAAM,CAAC,cAAc,EAAE,CAAC;QACxB,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC7B;IAED,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,IAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAE5B,IAAI,CAAC,GAAG,IAAI,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAElB;aAAM,IAAI,CAAC,GAAG,KAAK,EAAE;YAClB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAElC;aAAM,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,MAAM,EAAE;YAC/B,CAAC,EAAE,CAAC;YACJ,IAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAE7B,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE;gBAC7C,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;aAC3C;YAED,iBAAiB;YACjB,IAAM,IAAI,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAErC;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;SAClC;KACJ;IAED,OAAO,IAAA,gBAAQ,EAAC,MAAM,CAAC,CAAC;AAC5B,CAAC;AAzCD,kCAyCC;AAAA,CAAC;AAEF,SAAS,UAAU,CAAC,KAAa;IAC7B,IAAM,GAAG,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1C,OAAO,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACjD,CAAC;AAED,SAAgB,oBAAoB,CAAC,KAAgB,EAAE,OAAuB;IAC1E,OAAO,GAAG,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,UAAC,SAAS;QACzD,IAAI,SAAS,GAAG,GAAG,EAAE;YACjB,QAAQ,SAAS,EAAE;gBACf,KAAK,CAAC,CAAC,CAAE,OAAO,KAAK,CAAC;gBACtB,KAAK,CAAC,CAAC,CAAE,OAAO,KAAK,CAAC;gBACtB,KAAK,EAAE,CAAC,CAAC,OAAO,KAAK,CAAA;gBACrB,KAAK,EAAE,CAAC,CAAC,OAAO,KAAK,CAAC;gBACtB,KAAK,EAAE,CAAC,CAAC,OAAO,MAAM,CAAC;gBACvB,KAAK,EAAE,CAAC,CAAC,OAAO,MAAM,CAAC;aAC1B;YAED,IAAI,SAAS,IAAI,EAAE,IAAI,SAAS,GAAG,GAAG,EAAE;gBACpC,OAAO,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACzC;SACJ;QAED,IAAI,SAAS,IAAI,MAAM,EAAE;YACrB,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC;SAChC;QAED,SAAS,IAAI,OAAO,CAAC;QACrB,OAAO,UAAU,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;IACvG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;AACtB,CAAC;AAxBD,oDAwBC;AAED,SAAgB,aAAa,CAAC,UAAyB;IACnD,OAAO,UAAU,CAAC,GAAG,CAAC,UAAC,SAAS;QAC5B,IAAI,SAAS,IAAI,MAAM,EAAE;YACrB,OAAO,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;SACzC;QACD,SAAS,IAAI,OAAO,CAAC;QACrB,OAAO,MAAM,CAAC,YAAY,CACtB,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,EACtC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CACjC,CAAC;IACN,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAChB,CAAC;AAXD,sCAWC;AAED,SAAgB,YAAY,CAAC,KAAgB,EAAE,OAAuB;IAClE,OAAO,aAAa,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5D,CAAC;AAFD,oCAEC;AAED,SAAgB,gBAAgB,CAAC,GAAW,EAAE,IAAiE;IAAjE,qBAAA,EAAA,OAAiC,wBAAwB,CAAC,OAAO;IAC3G,OAAO,iBAAiB,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACrD,CAAC;AAFD,4CAEC"}