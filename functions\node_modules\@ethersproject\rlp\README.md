Recursive-Length Prefix Coder
=============================

This sub-module is part of the [ethers project](https://github.com/ethers-io/ethers.js).

It contains functions for encoding and decoding RLP data.

For more information, see the [documentation](https://docs.ethers.io/v5/api/utils/encoding/#rlp--methods).


Importing
---------

Most users will prefer to use the [umbrella package](https://www.npmjs.com/package/ethers),
but for those with more specific needs, individual components can be imported.

```javascript
const {

    encode,
    decode

} = require("@ethersproject/rlp");
```


License
-------

MIT License
