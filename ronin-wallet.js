  // Ronin Wallet Connect Script for Desktop & Mobile (with deep link & legacy fallback)
  // Add helper function to check if user agent is mobile device
  export function isMobile() {
    return /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  }
  
  export function getRoninProvider() {
    // Check for modern Ronin provider
    let provider = null;
    if (window.ronin && window.ronin.provider) provider = window.ronin.provider;
    
    // Check for legacy Ronin provider
    if (!provider && window.ronin && typeof window.ronin.request === 'function') provider = window.ronin;
    
    // Check for injected Ethereum provider with Ronin flag
    if (!provider && window.ethereum && window.ethereum.isRonin && typeof window.ethereum.request === 'function') provider = window.ethereum;
    
    // Check for multiple providers (e.g., MetaMask + Ronin)
    if (!provider && window.ethereum && Array.isArray(window.ethereum.providers)) {
      const roninProvider = window.ethereum.providers.find(
        (p) => p.isRonin && typeof p.request === 'function'
      );
      if (roninProvider) provider = roninProvider;
    }
    
    // Legacy/compat mode fallback
    if (!provider && window.ronin && typeof window.ronin.sendAsync === 'function') provider = window.ronin;
    if (!provider && window.ethereum && window.ethereum.isRonin && typeof window.ethereum.sendAsync === 'function') provider = window.ethereum;
    
    return provider;
  }
  
  // Add helper function to check if provider supports events
  export function supportsEvents(provider) {
    return provider && typeof provider.on === 'function' && typeof provider.removeListener === 'function';
  }
  
  // Add helper function to safely setup event listeners
  export function setupProviderEvents(provider, handlers) {
    if (!supportsEvents(provider)) {
      console.warn('Provider does not support event listeners');
      return () => {}; // Return empty cleanup function
    }
  
    // Setup event listeners
    if (handlers.accountsChanged) {
      provider.on('accountsChanged', handlers.accountsChanged);
    }
    if (handlers.chainChanged) {
      provider.on('chainChanged', handlers.chainChanged);
    }
    if (handlers.disconnect) {
      provider.on('disconnect', handlers.disconnect);
    }
  
    // Return cleanup function
    return () => {
      if (handlers.accountsChanged) {
        provider.removeListener('accountsChanged', handlers.accountsChanged);
      }
      if (handlers.chainChanged) {
        provider.removeListener('chainChanged', handlers.chainChanged);
      }
      if (handlers.disconnect) {
        provider.removeListener('disconnect', handlers.disconnect);
      }
    };
  }
  
  // For legacy providers (sendAsync)
  function requestAccountsLegacy(provider) {
    return new Promise((resolve, reject) => {
      provider.sendAsync(
        {
          method: 'eth_requestAccounts',
          params: [],
          id: 1,
          jsonrpc: '2.0',
        },
        (err, result) => {
          if (err) return reject(err);
          resolve(result.result);
        }
      );
    });
  }
  
  export async function connectRoninWallet() {
    const provider = getRoninProvider();
    if (!provider) {
      if (isMobile()) {
        // Deep link to Ronin Wallet app
        const dappUrl = encodeURIComponent(window.location.href);
        window.location.href = `https://wallet.roninchain.com/dapp/connect?dappUrl=${dappUrl}`;
      } else {
        // Show install Ronin Wallet prompt
        if (confirm('Ronin Wallet not detected. Would you like to install it?')) {
          window.open('https://wallet.roninchain.com/', '_blank');
        }
      }
      return null;
    }
  
    try {
      let accounts;
      if (typeof provider.request === 'function') {
        accounts = await provider.request({ method: 'eth_requestAccounts' });
      } else if (typeof provider.sendAsync === 'function') {
        accounts = await requestAccountsLegacy(provider);
      } else {
        throw new Error('Ronin Wallet provider does not support request or sendAsync.');
      }
  
      if (accounts && accounts.length > 0) {
        return accounts[0]; // Return the connected address
      } else {
        throw new Error('No accounts found in Ronin Wallet.');
      }
    } catch (err) {
      if (err.code === 4001) {
        throw new Error('User denied wallet connection.');
      } else {
        throw new Error('Wallet connection error: ' + (err.message || err));
      }
    }
  }
  
  // Example: Attach to button
  document.getElementById('connectWalletBtn').addEventListener('click', connectRoninWallet);
  
  // Default export
  export default {
    isMobile,
    getRoninProvider,
    connectRoninWallet,
    supportsEvents,
    setupProviderEvents
  };