{"version": 3, "file": "web3-provider.js", "sourceRoot": "", "sources": ["../src.ts/web3-provider.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAGb,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAErE,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAatD,IAAI,OAAO,GAAG,CAAC,CAAC;AAMhB,SAAS,sBAAsB,CAAC,QAA0B,EAAE,QAAwB;IAChF,MAAM,OAAO,GAAG,mBAAmB,CAAC;IAEpC,OAAO,UAAS,MAAc,EAAE,MAAkB;QAC9C,MAAM,OAAO,GAAG;YACZ,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,MAAM;YACd,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,EAAE,KAAK;SACjB,CAAC;QAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,EAAE,SAAS;gBACjB,OAAO;gBACP,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC;gBAC1B,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBAElC,IAAI,KAAK,EAAE;oBACP,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACf,MAAM,EAAE,UAAU;wBAClB,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,EAAE,IAAI;qBACjB,CAAC,CAAC;oBAEH,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;iBACxB;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACf,MAAM,EAAE,UAAU;oBAClB,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,QAAQ,EAAE,IAAI;iBACjB,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,KAAK,EAAE;oBAChB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBAC1C,KAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;oBAClC,KAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;oBACxC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;iBACxB;gBAED,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC,CAAA;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,QAA0B;IACnD,OAAO,UAAS,MAAc,EAAE,MAAkB;QAC9C,IAAI,MAAM,IAAI,IAAI,EAAE;YAAE,MAAM,GAAG,EAAG,CAAC;SAAE;QAErC,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QAEnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,gBAAgB;YACzB,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC;YAC1B,QAAQ,EAAE,IAAI;SACjB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,gBAAgB;gBACzB,OAAO;gBACP,QAAQ;gBACR,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAEpB,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,gBAAgB;gBACzB,OAAO;gBACP,KAAK;gBACL,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC,CAAA;AACL,CAAC;AAED,MAAM,OAAO,YAAa,SAAQ,eAAe;IAI7C,YAAY,QAA6C,EAAE,OAAoB;QAC3E,IAAI,QAAQ,IAAI,IAAI,EAAE;YAClB,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SACvE;QAED,IAAI,IAAI,GAAW,IAAI,CAAC;QACxB,IAAI,gBAAgB,GAAqB,IAAI,CAAC;QAC9C,IAAI,WAAW,GAAqB,IAAI,CAAC;QAEzC,IAAI,OAAM,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;YACjC,IAAI,GAAG,UAAU,CAAC;YAClB,gBAAgB,GAAG,QAAQ,CAAC;SAE/B;aAAM;YACH,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAC9B,IAAI,GAAG,UAAU,CAAC;aACrB;YAED,WAAW,GAAG,QAAQ,CAAC;YAEvB,IAAI,QAAQ,CAAC,OAAO,EAAE;gBAClB,IAAI,IAAI,KAAK,EAAE,EAAE;oBAAE,IAAI,GAAG,WAAW,CAAC;iBAAE;gBACxC,gBAAgB,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;aACpD;iBAAM,IAAI,QAAQ,CAAC,SAAS,EAAE;gBAC3B,gBAAgB,GAAG,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC1F;iBAAM,IAAI,QAAQ,CAAC,IAAI,EAAE;gBACtB,gBAAgB,GAAG,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;aACrF;iBAAM;gBACH,MAAM,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;aAC3E;YAED,IAAI,CAAC,IAAI,EAAE;gBAAE,IAAI,GAAG,UAAU,CAAC;aAAE;SACpC;QAED,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAErB,cAAc,CAAC,IAAI,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;QAC3D,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,CAAC,MAAc,EAAE,MAAkB;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;CACJ"}