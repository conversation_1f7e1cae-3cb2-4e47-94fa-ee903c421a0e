{"version": 3, "file": "lib.js", "sourceRoot": "", "sources": ["../../src.ts/ens-normalize/lib.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAE1D,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,MAAM,CAAC,GAAG,OAAO,EAAE,CAAC;AAEpB,OAAO,EAAC,iBAAiB,EAAE,eAAe,EAAE,eAAe,EAAC,MAAM,cAAc,CAAC;AAEjF,sCAAsC;AAEtC,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AACtC,oGAAoG;AAEpG,oBAAoB;AACpB,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,MAAM,UAAU,GAAG,IAAI,CAAC;AAExB,SAAS,UAAU,CAAC,IAAY;IAC5B,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,WAAW,CAAC,GAAkB;IACnC,OAAO,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC;AAC1C,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,IAAY;IACpD,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QAClC,IAAI,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI;YACH,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1D,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;oBAC1B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;iBACpD;aACD;YACD,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;gBAC5F,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;aAC3C;SACD;QAAC,OAAO,GAAG,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,kBAAkB,KAAK,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;SAC5D;KACD;IACD,OAAO,IAAI,CAAC;AACb,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,IAAY;IACzC,OAAO,wBAAwB,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,SAAS,CAAC,IAAY,EAAE,YAAiD;IACjF,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe;IACvD,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,OAAO,KAAK,CAAC,MAAM,EAAE;QACpB,IAAI,KAAK,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACpC,SAAS;SACT;QACD,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACrB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAClB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChB,SAAS;SACT;QACD,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACpB,SAAS;SACT;QACD,IAAI,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QACrB,IAAI,GAAG,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;YACpB,SAAS;SACT;QACD,MAAM,IAAI,KAAK,CAAC,2BAA2B,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;KAC5E;IACD,OAAO,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,SAAS,GAAG,CAAC,CAAS;IAClB,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAkB,EAAE,KAAqB;;IACxE,IAAI,IAAI,GAAG,UAAU,CAAC;IACtB,IAAI,KAAK,CAAC;IACV,IAAI,KAAK,CAAC;IACV,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACrB,IAAI,KAAK;QAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,iCAAiC;IAC9D,OAAO,GAAG,EAAE;QACX,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,GAAG,MAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,0CAAE,IAAI,CAAC;QACpD,IAAI,CAAC,IAAI;YAAE,MAAM;QACjB,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW;YAC3B,KAAK,GAAG,EAAE,CAAC;SACX;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,kBAAkB;YAC1C,IAAI,EAAE,KAAK,KAAK;gBAAE,MAAM;SACxB;QACD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,IAAI,IAAI,CAAC,IAAI,EAAE;YACd,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnB,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,MAAM;gBAAE,GAAG,EAAE,CAAC,CAAC,wBAAwB;SACtE;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,iCAAiC;YAClD,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,aAAa;YACpC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;gBAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yDAAyD;YAClG,IAAI,KAAK;gBAAE,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,yBAAyB;YAC7E,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,WAAW;SAC7B;KACD;IACD,OAAO,KAAK,CAAC;AACd,CAAC"}