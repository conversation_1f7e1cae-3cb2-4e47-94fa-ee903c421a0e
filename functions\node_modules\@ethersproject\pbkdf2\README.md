Password-Based Key Derivation Function 2 (pbkdf2)
=================================================

This sub-module is part of the [ethers project](https://github.com/ethers-io/ethers.js).

It contains the PBKDF2 function.

For more information, see the [documentation](https://docs.ethers.io/v5/api/utils/).

Importing
---------

Most users will prefer to use the [umbrella package](https://www.npmjs.com/package/ethers),
but for those with more specific needs, individual components can be imported.

```javascript
const {

    pbkdf2

} = require("@ethersproject/pbkdf2");
```


License
-------

MIT License
