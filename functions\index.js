const functions = require('firebase-functions/v1');
const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}

const { generateBracket } = require('./bracketGenerator');
const { scheduledTournamentStatusUpdate } = require('./scheduledStatusUpdate');
const { verifyPayment } = require('./verifyPayment');
const { generateBracketForTournament } = require('./generateBracketForTournament');

exports.generateBracketAfterRegistrationClosed = functions.firestore
    .document('tournaments/{tournamentId}')
    .onUpdate(async (change, context) => {
        const newData = change.after.data();
        const previousData = change.before.data();
        const tournamentId = context.params.tournamentId;

        // Check if the status changed to 'registrationClosed'
        if (newData.status === 'registrationClosed' && previousData.status !== 'registrationClosed') {
            console.log(`Registration closed for tournament: ${tournamentId}. Generating bracket...`);

            // Generate bracket if there are participants
            if (newData.participants && newData.participants.length > 0) {
                try {
                    const participants = newData.participants.map((address, index) => ({
                        id: address,
                        name: `Player ${index + 1}`,
                        seed: index + 1
                    }));

                    const bracketData = generateBracket(
                        participants,
                        newData.tournamentFormat || 'single-elim'
                    );

                    // Update the tournament with the bracket data
                    await change.after.ref.update({
                        bracketData: bracketData
                    });

                    console.log(`Bracket generated for tournament: ${tournamentId}`);
                } catch (error) {
                    console.error(`Error generating bracket for tournament ${tournamentId}:`, error);
                }
            } else {
                console.log(`No participants for tournament: ${tournamentId}`);
            }
        }
        return null;
    });

// Function to manually update tournament status
exports.regenerateBracket = functions.https.onRequest(async (req, res) => {
    const db = admin.firestore();
    const tournamentsRef = db.collection('tournaments');

    try {
        // Get tournaments that are in 'live' status
        const snapshot = await tournamentsRef
            .where('status', '==', 'live')
            .get();

        const batch = db.batch();
        const now = admin.firestore.Timestamp.now();

        snapshot.forEach(doc => {
            // First update to registrationClosed
            batch.update(doc.ref, {
                status: 'registrationClosed',
                registrationClosedAt: now
            });
        });

        // Commit the batch to update all tournaments to registrationClosed
        await batch.commit();

        // Wait a moment for the bracket generation to complete
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Now update back to live
        const batch2 = db.batch();
        snapshot.forEach(doc => {
            batch2.update(doc.ref, {
                status: 'live',
                startedAt: now
            });
        });

        // Commit the second batch to update all tournaments back to live
        await batch2.commit();

        res.json({
            message: 'Successfully triggered bracket regeneration for live tournaments',
            count: snapshot.size
        });
    } catch (error) {
        console.error('Error updating tournaments:', error);
        res.status(500).json({ error: error.message });
    }
});

// Function to check tournament data and debug status updates
exports.checkTournamentData = functions.https.onRequest(async (req, res) => {
    const db = admin.firestore();
    const tournamentsRef = db.collection('tournaments');

    try {
        // Get all tournaments to debug status issues
        const allSnapshot = await tournamentsRef.get();
        const now = admin.firestore.Timestamp.now();

        const tournaments = [];
        allSnapshot.forEach(doc => {
            const data = doc.data();
            const startDate = data.startDate;
            const registrationClosedAt = data.registrationClosedAt;

            tournaments.push({
                id: doc.id,
                status: data.status,
                startDate: startDate?.toDate(),
                registrationClosedAt: registrationClosedAt?.toDate(),
                shouldCloseRegistration: startDate && startDate.seconds <= now.seconds,
                shouldStartTournament: registrationClosedAt && (now.seconds - registrationClosedAt.seconds) >= (30 * 60),
                hasBracketData: !!data.bracketData,
                bracketFormat: data.bracketData?.format,
                numRounds: data.bracketData?.rounds?.length,
                numParticipants: data.participants?.length || 0,
                startedAt: data.startedAt?.toDate()
            });
        });

        res.json({
            currentTime: now.toDate(),
            count: tournaments.length,
            tournaments: tournaments
        });
    } catch (error) {
        console.error('Error fetching tournaments:', error);
        res.status(500).json({ error: error.message });
    }
});

// Manual function to force tournament status updates for debugging
exports.manualTournamentStatusUpdate = functions.https.onRequest(async (req, res) => {
    const db = admin.firestore();
    const now = admin.firestore.Timestamp.now();
    const tournamentsRef = db.collection('tournaments');

    try {
        let updatedCount = 0;
        const results = [];

        // First, handle tournaments that need to close registration
        const registrationSnapshot = await tournamentsRef
            .where('status', '==', 'upcoming')
            .where('startDate', '<=', now)
            .get();

        if (!registrationSnapshot.empty) {
            const registrationBatch = db.batch();

            for (const doc of registrationSnapshot.docs) {
                const tournament = doc.data();

                // Generate bracket if there are participants
                if (tournament.participants && tournament.participants.length > 0) {
                    try {
                        const participants = tournament.participants.map((address, index) => ({
                            id: address,
                            name: `Player ${index + 1}`,
                            seed: index + 1
                        }));

                        const bracketData = generateBracket(
                            participants,
                            tournament.tournamentFormat || 'single-elim'
                        );

                        registrationBatch.update(doc.ref, {
                            status: 'registrationClosed',
                            registrationClosedAt: now,
                            bracketData: bracketData
                        });

                        results.push({
                            id: doc.id,
                            action: 'closed_registration_with_bracket',
                            participants: tournament.participants.length
                        });
                    } catch (error) {
                        console.error(`Error generating bracket for tournament ${doc.id}:`, error);
                        // Still update status even if bracket generation fails
                        registrationBatch.update(doc.ref, {
                            status: 'registrationClosed',
                            registrationClosedAt: now
                        });

                        results.push({
                            id: doc.id,
                            action: 'closed_registration_no_bracket',
                            error: error.message
                        });
                    }
                } else {
                    // No participants, just update status
                    registrationBatch.update(doc.ref, {
                        status: 'registrationClosed',
                        registrationClosedAt: now
                    });

                    results.push({
                        id: doc.id,
                        action: 'closed_registration_no_participants'
                    });
                }

                updatedCount++;
            }

            await registrationBatch.commit();
        }

        // Then, handle tournaments that should start (30 minutes after registration closed)
        const thirtyMinutesAgo = new admin.firestore.Timestamp(
            now.seconds - (30 * 60), // 30 minutes in seconds
            now.nanoseconds
        );

        const startSnapshot = await tournamentsRef
            .where('status', '==', 'registrationClosed')
            .where('registrationClosedAt', '<=', thirtyMinutesAgo)
            .get();

        if (!startSnapshot.empty) {
            const startBatch = db.batch();
            startSnapshot.forEach(doc => {
                startBatch.update(doc.ref, {
                    status: 'live',
                    startedAt: now
                });

                results.push({
                    id: doc.id,
                    action: 'started_tournament'
                });

                updatedCount++;
            });
            await startBatch.commit();
        }

        res.json({
            message: 'Manual tournament status update completed',
            currentTime: now.toDate(),
            updatedCount,
            results
        });
    } catch (error) {
        console.error('Error in manual tournament status update:', error);
        res.status(500).json({ error: error.message });
    }
});

exports.scheduledTournamentStatusUpdate = scheduledTournamentStatusUpdate;

// Export the verifyPayment function
exports.verifyPayment = verifyPayment;

// Export the generateBracketForTournament function
exports.generateBracketForTournament = functions.https.onCall(async (data, context) => {
    // Check if user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { tournamentId } = data;
    if (!tournamentId) {
        throw new functions.https.HttpsError('invalid-argument', 'Tournament ID is required');
    }

    try {
        const bracketData = await generateBracketForTournament(tournamentId);
        return { success: true, bracketData };
    } catch (error) {
        console.error('Error generating bracket:', error);
        throw new functions.https.HttpsError('internal', error.message);
    }
});

// Export the regenerateBracketForLiveTournament function
exports.regenerateBracketForLiveTournament = functions.https.onCall(async (data, context) => {
    // Check if user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { tournamentId } = data;
    if (!tournamentId) {
        throw new functions.https.HttpsError('invalid-argument', 'Tournament ID is required');
    }

    try {
        const db = admin.firestore();
        const tournamentRef = db.collection('tournaments').doc(tournamentId);
        const tournamentDoc = await tournamentRef.get();

        if (!tournamentDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Tournament not found');
        }

        const tournament = tournamentDoc.data();

        // Verify tournament is live
        if (tournament.status !== 'live') {
            throw new functions.https.HttpsError('failed-precondition', 'Tournament must be live to regenerate bracket');
        }

        // Generate new bracket
        const bracketData = await generateBracketForTournament(tournamentId);

        return { success: true, message: 'Bracket regenerated successfully', bracketData };
    } catch (error) {
        console.error('Error regenerating bracket:', error);
        throw new functions.https.HttpsError('internal', error.message);
    }
});

// Cloud Function to update match results
exports.updateMatchResult = functions.https.onCall(async (data, context) => {
    // Check if user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated to update match results');
    }

    const { tournamentId, matchId, result } = data;
    if (!tournamentId || !matchId || !result) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    const db = admin.firestore();
    const tournamentRef = db.collection('tournaments').doc(tournamentId);

    try {
        // Use a transaction to ensure atomicity
        await db.runTransaction(async (transaction) => {
            const tournamentDoc = await transaction.get(tournamentRef);
            if (!tournamentDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'Tournament not found');
            }

            const tournament = tournamentDoc.data();

            // Get the user's wallet address from the request
            const userWalletAddress = data.userWalletAddress;
            if (!userWalletAddress) {
                throw new functions.https.HttpsError('invalid-argument', 'User wallet address is required');
            }

            // Verify user is a participant using wallet address
            const isParticipant = tournament.participants.some(p =>
                (typeof p === 'string' && p === userWalletAddress) ||
                (typeof p === 'object' && p.id === userWalletAddress)
            );

            if (!isParticipant) {
                throw new functions.https.HttpsError('permission-denied', 'User is not a participant in this tournament');
            }

            // Verify tournament is in progress
            if (tournament.status !== 'live') {
                throw new functions.https.HttpsError('failed-precondition', 'Tournament is not in progress');
            }

            // Find and update the match
            const match = tournament.bracketData.matchesById[matchId];
            if (!match) {
                throw new functions.https.HttpsError('not-found', 'Match not found');
            }

            // Verify the user is a participant in this match using wallet address
            if (match.participant1Id !== userWalletAddress && match.participant2Id !== userWalletAddress) {
                throw new functions.https.HttpsError('permission-denied', 'User is not a participant in this match');
            }

            // Update match result
            match.status = 'completed';
            match.winnerId = result.winnerId;
            match.loserId = result.winnerId === match.participant1Id ? match.participant2Id : match.participant1Id;
            match.score = result.score;
            match.completedAt = admin.firestore.Timestamp.now();

            // Sync the match data in the rounds array
            tournament.bracketData.rounds.forEach(round => {
                round.matches.forEach(roundMatch => {
                    if (roundMatch.id === match.id) {
                        roundMatch.status = match.status;
                        roundMatch.winnerId = match.winnerId;
                        roundMatch.loserId = match.loserId;
                        roundMatch.score = match.score;
                        roundMatch.completedAt = match.completedAt;
                    }
                });
            });

            // Handle bracket progression based on tournament format
            switch (tournament.bracketData.format) {
                case 'single-elimination':
                    await handleSingleEliminationProgression(transaction, tournament, match, tournamentRef);
                    break;
                case 'double-elimination':
                    await handleDoubleEliminationProgression(transaction, tournament, match, tournamentRef);
                    break;
                case 'round-robin':
                    await handleRoundRobinProgression(transaction, tournament, match, tournamentRef);
                    break;
            }

            // Update the tournament document
            transaction.update(tournamentRef, {
                bracketData: tournament.bracketData
            });
        });

        return { success: true };
    } catch (error) {
        console.error('Error updating match result:', error);
        throw new functions.https.HttpsError('internal', error.message);
    }
});

// Helper functions for bracket progression
async function handleSingleEliminationProgression(transaction, tournament, completedMatch, tournamentRef) {
    const { matchesById } = tournament.bracketData;

    // Find the next match using nextMatchId
    const nextMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;

    if (nextMatch) {
        // Update the next match with the winner
        if (nextMatch.participant1Id === null) {
            nextMatch.participant1Id = completedMatch.winnerId;
        } else {
            nextMatch.participant2Id = completedMatch.winnerId;
        }

        // Check if both participants are now set
        if (nextMatch.participant1Id && nextMatch.participant2Id) {
            nextMatch.status = 'ready';
        }

        // Sync the next match data in the rounds array
        tournament.bracketData.rounds.forEach(round => {
            round.matches.forEach(roundMatch => {
                if (roundMatch.id === nextMatch.id) {
                    roundMatch.participant1Id = nextMatch.participant1Id;
                    roundMatch.participant2Id = nextMatch.participant2Id;
                    roundMatch.status = nextMatch.status;
                }
            });
        });
    } else {
        // If there's no next match, this was the final match
        // Update tournament status to completed
        transaction.update(tournamentRef, {
            status: 'completed',
            completedAt: admin.firestore.Timestamp.now(),
            winner: completedMatch.winnerId
        });
    }
}

async function handleDoubleEliminationProgression(transaction, tournament, completedMatch, tournamentRef) {
    const { matchesById } = tournament.bracketData;

    // Handle winner's bracket progression
    if (completedMatch.isUpperBracket) {
        // Find next upper bracket match using nextMatchId
        const nextUpperMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;

        if (nextUpperMatch) {
            if (nextUpperMatch.participant1Id === null) {
                nextUpperMatch.participant1Id = completedMatch.winnerId;
            } else {
                nextUpperMatch.participant2Id = completedMatch.winnerId;
            }
        }

        // Handle loser's bracket progression using nextLoserMatchId
        const loserNextMatch = completedMatch.nextLoserMatchId ? matchesById[completedMatch.nextLoserMatchId] : null;

        if (loserNextMatch) {
            if (loserNextMatch.participant1Id === null) {
                loserNextMatch.participant1Id = completedMatch.loserId;
            } else {
                loserNextMatch.participant2Id = completedMatch.loserId;
            }
        }
    } else {
        // Handle loser's bracket progression
        const nextLowerMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;

        if (nextLowerMatch) {
            if (nextLowerMatch.participant1Id === null) {
                nextLowerMatch.participant1Id = completedMatch.winnerId;
            } else {
                nextLowerMatch.participant2Id = completedMatch.winnerId;
            }
        } else {
            // If there's no next match in the loser's bracket, this was the final match
            // Update tournament status to completed
            transaction.update(tournamentRef, {
                status: 'completed',
                completedAt: admin.firestore.Timestamp.now(),
                winner: completedMatch.winnerId
            });
        }
    }

    // Update match status to ready if both participants are set
    Object.values(matchesById).forEach(match => {
        if (match.status === 'pending' && match.participant1Id && match.participant2Id) {
            match.status = 'ready';
        }
    });
}

async function handleRoundRobinProgression(transaction, tournament, completedMatch, tournamentRef) {
    const { matchesById } = tournament.bracketData;

    // Check if all matches are completed
    const allMatchesCompleted = Object.values(matchesById).every(match => match.status === 'completed');

    if (allMatchesCompleted) {
        // Update tournament status to completed
        transaction.update(tournamentRef, {
            status: 'completed',
            completedAt: admin.firestore.Timestamp.now()
        });
    }
}